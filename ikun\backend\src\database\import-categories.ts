/**
 * 临时分类导入脚本
 * 用于将 categories.json 中的分类数据导入到数据库
 */

// 直接使用 mysql2 连接，避免路径别名问题
import mysql from 'mysql2/promise';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// 加载环境变量
dotenv.config();

// 简单的日志函数
const log = {
  info: (message: string) => console.log(`[INFO] ${new Date().toISOString()} - ${message}`),
  warn: (message: string) => console.warn(`[WARN] ${new Date().toISOString()} - ${message}`),
  error: (message: string, error?: any) => {
    console.error(`[ERROR] ${new Date().toISOString()} - ${message}`);
    if (error) console.error(error);
  }
};

interface CategoryData {
  category_level: number;
  english_name: string;
  chinese_name: string;
  subcategories?: CategoryData[];
}

interface FlatCategory {
  category_code: string | null;
  chinese_name: string;
  english_name: string;
  status: 'enabled' | 'disabled';
  category_level: number;
  parent_id: number | null;
  category_description: string;
  attribute_tags: string;
  category_path: string;
}

class CategoryImporter {
  private categoryMap = new Map<string, number>(); // 存储分类路径到ID的映射
  private connection: mysql.Connection | null = null;

  /**
   * 创建数据库连接
   */
  private async createConnection(): Promise<mysql.Connection> {
    if (this.connection) {
      return this.connection;
    }

    this.connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'ikun',
      charset: 'utf8mb4'
    });

    return this.connection;
  }

  /**
   * 生成分类路径
   */
  private generateCategoryPath(englishName: string, parentPath?: string): string {
    const pathSegment = englishName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
    
    return parentPath ? `${parentPath}/${pathSegment}` : pathSegment;
  }

  /**
   * 递归扁平化分类树
   */
  private flattenCategories(
    categories: CategoryData[], 
    parentPath: string = '', 
    parentId: number | null = null
  ): FlatCategory[] {
    const result: FlatCategory[] = [];

    for (const category of categories) {
      const categoryPath = this.generateCategoryPath(category.english_name, parentPath);
      
      const flatCategory: FlatCategory = {
        category_code: null, // 分类编码全部为空
        chinese_name: category.chinese_name,
        english_name: category.english_name,
        status: 'enabled', // 状态全部启用
        category_level: category.category_level,
        parent_id: parentId,
        category_description: '', // 描述全部为空
        attribute_tags: '[]', // 属性标签全部为空数组
        category_path: categoryPath
      };

      result.push(flatCategory);

      // 递归处理子分类
      if (category.subcategories && category.subcategories.length > 0) {
        const childCategories = this.flattenCategories(
          category.subcategories,
          categoryPath,
          null // 这里先设为null，后面会更新为实际的父ID
        );
        result.push(...childCategories);
      }
    }

    return result;
  }

  /**
   * 导入分类到数据库
   */
  public async importCategories(): Promise<void> {
    const connection = await this.createConnection();

    try {
      log.info('开始导入分类数据...');

      // 读取JSON文件
      const jsonPath = path.join(__dirname, 'categories.json');
      const jsonData = fs.readFileSync(jsonPath, 'utf-8');
      const categories: CategoryData[] = JSON.parse(jsonData);

      // 扁平化分类数据
      const flatCategories = this.flattenCategories(categories);
      log.info(`共找到 ${flatCategories.length} 个分类`);

      // 按层级排序，确保父分类先创建
      flatCategories.sort((a, b) => a.category_level - b.category_level);

      // 开始事务
      await connection.execute('START TRANSACTION');

      try {
        // 清空现有分类数据（可选，根据需要决定是否启用）
        // await connection.execute('DELETE FROM product_categories');
        // await connection.execute('ALTER TABLE product_categories AUTO_INCREMENT = 10001');

        let importedCount = 0;

        for (const category of flatCategories) {
          // 如果有父分类，查找父分类ID
          let parentId = null;
          if (category.category_level > 1) {
            const parentPath = category.category_path.split('/').slice(0, -1).join('/');
            parentId = this.categoryMap.get(parentPath) || null;

            if (!parentId) {
              log.warn(`找不到父分类，跳过: ${category.english_name} (路径: ${category.category_path})`);
              continue;
            }
          }

          // 插入分类
          const result = await connection.execute(`
            INSERT INTO product_categories (
              category_code, chinese_name, english_name, status, 
              category_level, parent_id, category_description, 
              attribute_tags, category_path, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
          `, [
            category.category_code,
            category.chinese_name,
            category.english_name,
            category.status,
            category.category_level,
            parentId,
            category.category_description,
            category.attribute_tags,
            category.category_path
          ]);

          // 存储分类路径到ID的映射
          const insertId = (result as any).insertId;
          this.categoryMap.set(category.category_path, insertId);

          importedCount++;

          if (importedCount % 100 === 0) {
            log.info(`已导入 ${importedCount} 个分类...`);
          }
        }

        // 提交事务
        await connection.execute('COMMIT');
        log.info(`分类导入完成！共导入 ${importedCount} 个分类`);

      } catch (error) {
        // 回滚事务
        await connection.execute('ROLLBACK');
        throw error;
      }

    } catch (error) {
      log.error('分类导入失败:', error);
      throw error;
    } finally {
      // 关闭数据库连接
      if (this.connection) {
        await this.connection.end();
      }
    }
  }
}

// 执行导入
async function runImport() {
  const importer = new CategoryImporter();

  try {
    await importer.importCategories();
    log.info('分类导入脚本执行完成');
    process.exit(0);
  } catch (error) {
    log.error('分类导入脚本执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runImport();
}

export { CategoryImporter };
