/**
 * 最简单的分类导入脚本
 * 直接运行: node simple-import.js
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置
const dbConfig = {
  host: '127.0.0.1',
  port: 3306,
  user: 'root',
  password: '123456',
  database: 'ikun',
  charset: 'utf8mb4'
};

// 生成分类路径
function generateCategoryPath(englishName, parentPath = '') {
  const pathSegment = englishName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
  
  return parentPath ? `${parentPath}/${pathSegment}` : pathSegment;
}

// 递归扁平化分类树
function flattenCategories(categories, parentPath = '', parentId = null) {
  const result = [];

  for (const category of categories) {
    const categoryPath = generateCategoryPath(category.english_name, parentPath);
    
    const flatCategory = {
      category_platform: 'local',
      category_code: null,
      category__namecn: category.chinese_name,
      category__name: category.english_name,
      category_status: 'enabled',
      category_level: category.category_level,
      category_parent_id: parentId,
      category_description: '',
      category_attribute_tags: '[]',
      category_path: categoryPath,
      category__mapping_local: null
    };

    result.push(flatCategory);

    // 递归处理子分类
    if (category.subcategories && category.subcategories.length > 0) {
      const childCategories = flattenCategories(
        category.subcategories,
        categoryPath,
        null // 这里先设为null，后面会更新为实际的父ID
      );
      result.push(...childCategories);
    }
  }

  return result;
}

// 主函数
async function importCategories() {
  let connection;
  
  try {
    console.log('开始导入分类数据...');

    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');

    // 读取JSON文件
    const jsonPath = path.join(__dirname, 'src/database/categories.json');
    const jsonData = fs.readFileSync(jsonPath, 'utf-8');
    const categories = JSON.parse(jsonData);

    // 扁平化分类数据
    const flatCategories = flattenCategories(categories);
    console.log(`共找到 ${flatCategories.length} 个分类`);

    // 按层级排序，确保父分类先创建
    flatCategories.sort((a, b) => a.category_level - b.category_level);

    // 开始事务
    await connection.query('START TRANSACTION');

    try {
      const categoryMap = new Map(); // 存储分类路径到ID的映射
      let importedCount = 0;

      for (const category of flatCategories) {
        // 如果有父分类，查找父分类ID
        let parentId = null;
        if (category.category_level > 1) {
          const parentPath = category.category_path.split('/').slice(0, -1).join('/');
          parentId = categoryMap.get(parentPath) || null;

          if (!parentId) {
            console.warn(`找不到父分类，跳过: ${category.english_name} (路径: ${category.category_path})`);
            continue;
          }
        }

        // 转义字符串以防止SQL注入
        const escapedValues = [
          `'${category.category_platform}'`,
          category.category_code ? `'${category.category_code.replace(/'/g, "''")}'` : 'NULL',
          `'${category.category__namecn.replace(/'/g, "''")}'`,
          `'${category.category__name.replace(/'/g, "''")}'`,
          `'${category.category_status}'`,
          category.category_level,
          parentId ? parentId : 'NULL',
          `'${category.category_description.replace(/'/g, "''")}'`,
          `'${category.category_attribute_tags.replace(/'/g, "''")}'`,
          `'${category.category_path.replace(/'/g, "''")}'`,
          category.category__mapping_local ? `'${JSON.stringify(category.category__mapping_local).replace(/'/g, "''")}'` : 'NULL'
        ];

        // 插入分类
        const [result] = await connection.query(`
          INSERT INTO product_categories (
            category_platform, category_code, category__namecn, category__name, category_status,
            category_level, category_parent_id, category_description,
            category_attribute_tags, category_path, category__mapping_local
          ) VALUES (${escapedValues.join(', ')})
        `);

        // 存储分类路径到ID的映射
        const insertId = result.insertId;
        categoryMap.set(category.category_path, insertId);

        importedCount++;

        if (importedCount % 100 === 0) {
          console.log(`已导入 ${importedCount} 个分类...`);
        }
      }

      // 提交事务
      await connection.query('COMMIT');
      console.log(`分类导入完成！共导入 ${importedCount} 个分类`);

    } catch (error) {
      // 回滚事务
      await connection.query('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('分类导入失败:', error.message);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    if (connection) {
      await connection.end();
    }
  }
}

// 执行导入
importCategories();
