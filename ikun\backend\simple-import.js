/**
 * 最简单的分类导入脚本
 * 直接运行: node simple-import.js
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置
const dbConfig = {
  host: '127.0.0.1',
  port: 3306,
  user: 'root',
  password: '123456',
  database: 'ikun',
  charset: 'utf8mb4'
};

// 生成分类路径
function generateCategoryPath(englishName, parentPath = '') {
  const pathSegment = englishName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
  
  return parentPath ? `${parentPath}/${pathSegment}` : pathSegment;
}

// 递归扁平化分类树
function flattenCategories(categories, parentPath = '', parentId = null) {
  const result = [];

  for (const category of categories) {
    const categoryPath = generateCategoryPath(category.english_name, parentPath);
    
    const flatCategory = {
      category_code: null,
      chinese_name: category.chinese_name,
      english_name: category.english_name,
      status: 'enabled',
      category_level: category.category_level,
      parent_id: parentId,
      category_description: '',
      attribute_tags: '[]',
      category_path: categoryPath
    };

    result.push(flatCategory);

    // 递归处理子分类
    if (category.subcategories && category.subcategories.length > 0) {
      const childCategories = flattenCategories(
        category.subcategories,
        categoryPath,
        null // 这里先设为null，后面会更新为实际的父ID
      );
      result.push(...childCategories);
    }
  }

  return result;
}

// 主函数
async function importCategories() {
  let connection;
  
  try {
    console.log('开始导入分类数据...');

    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');

    // 读取JSON文件
    const jsonPath = path.join(__dirname, 'src/database/categories.json');
    const jsonData = fs.readFileSync(jsonPath, 'utf-8');
    const categories = JSON.parse(jsonData);

    // 扁平化分类数据
    const flatCategories = flattenCategories(categories);
    console.log(`共找到 ${flatCategories.length} 个分类`);

    // 按层级排序，确保父分类先创建
    flatCategories.sort((a, b) => a.category_level - b.category_level);

    // 开始事务
    await connection.execute('START TRANSACTION');

    try {
      const categoryMap = new Map(); // 存储分类路径到ID的映射
      let importedCount = 0;

      for (const category of flatCategories) {
        // 如果有父分类，查找父分类ID
        let parentId = null;
        if (category.category_level > 1) {
          const parentPath = category.category_path.split('/').slice(0, -1).join('/');
          parentId = categoryMap.get(parentPath) || null;
          
          if (!parentId) {
            console.warn(`找不到父分类，跳过: ${category.english_name} (路径: ${category.category_path})`);
            continue;
          }
        }

        // 插入分类
        const [result] = await connection.execute(`
          INSERT INTO product_categories (
            category_code, chinese_name, english_name, status, 
            category_level, parent_id, category_description, 
            attribute_tags, category_path, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          category.category_code,
          category.chinese_name,
          category.english_name,
          category.status,
          category.category_level,
          parentId,
          category.category_description,
          category.attribute_tags,
          category.category_path
        ]);

        // 存储分类路径到ID的映射
        const insertId = result.insertId;
        categoryMap.set(category.category_path, insertId);

        importedCount++;

        if (importedCount % 100 === 0) {
          console.log(`已导入 ${importedCount} 个分类...`);
        }
      }

      // 提交事务
      await connection.execute('COMMIT');
      console.log(`分类导入完成！共导入 ${importedCount} 个分类`);

    } catch (error) {
      // 回滚事务
      await connection.execute('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('分类导入失败:', error.message);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    if (connection) {
      await connection.end();
    }
  }
}

// 执行导入
importCategories();
