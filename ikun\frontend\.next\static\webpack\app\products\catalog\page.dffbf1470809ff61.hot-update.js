"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/catalog/page",{

/***/ "(app-pages-browser)/./src/components/products/product-catalog-page.tsx":
/*!**********************************************************!*\
  !*** ./src/components/products/product-catalog-page.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductCatalogPage: function() { return /* binding */ ProductCatalogPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/expand.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/hooks/useCategories */ \"(app-pages-browser)/./src/hooks/useCategories.ts\");\n/* __next_internal_client_entry_do_not_use__ ProductCatalogPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductCatalogPage() {\n    _s();\n    const [expandedNodes, setExpandedNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"1\",\n        \"2\"\n    ]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedNodeLevel, setSelectedNodeLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 存储选中节点的层级\n    ;\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdding, setIsAdding] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_11__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    // 使用API hooks\n    const { categoryTree: apiCategoryTree, loading: categoriesLoading, error: categoriesError, refetch: refetchCategories } = (0,_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategoryTree)();\n    const { createCategory, updateCategory, deleteCategory, loading: operationLoading } = (0,_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategories)();\n    // 转换API数据为显示格式\n    const convertCategoryToNode = (category)=>({\n            id: category.id.toString(),\n            name: category.chinese_name,\n            englishName: category.english_name,\n            categoryCode: category.category_code || \"\",\n            description: category.category_description || \"\",\n            tags: category.attribute_tags || [],\n            createdAt: new Date(category.created_at).toLocaleString(\"zh-CN\"),\n            status: category.status === \"enabled\" ? \"active\" : \"inactive\",\n            children: category.children ? category.children.map(convertCategoryToNode) : undefined\n        });\n    const catalogTree = apiCategoryTree ? apiCategoryTree.map(convertCategoryToNode) : [];\n    const toggleExpanded = (nodeId)=>{\n        setExpandedNodes((prev)=>prev.includes(nodeId) ? prev.filter((id)=>id !== nodeId) : [\n                ...prev,\n                nodeId\n            ]);\n    };\n    // 递归获取所有有子节点的节点ID\n    const getAllExpandableNodeIds = (nodes)=>{\n        const ids = [];\n        const traverse = (nodeList)=>{\n            nodeList.forEach((node)=>{\n                if (node.children && node.children.length > 0) {\n                    ids.push(node.id);\n                    traverse(node.children);\n                }\n            });\n        };\n        traverse(nodes);\n        return ids;\n    };\n    // 展开全部\n    const handleExpandAll = ()=>{\n        const allExpandableIds = getAllExpandableNodeIds(catalogTree);\n        setExpandedNodes(allExpandableIds);\n    };\n    // 折叠全部\n    const handleCollapseAll = ()=>{\n        setExpandedNodes([]);\n    };\n    // 递归搜索匹配的节点\n    const searchNodes = (nodes, searchTerm)=>{\n        if (!searchTerm.trim()) return nodes;\n        const results = [];\n        const lowerSearchTerm = searchTerm.toLowerCase();\n        const searchInNode = (node)=>{\n            var _node_categoryCode;\n            // 检查当前节点是否匹配\n            const nameMatch = node.name.toLowerCase().includes(lowerSearchTerm);\n            const englishNameMatch = node.englishName.toLowerCase().includes(lowerSearchTerm);\n            const codeMatch = (_node_categoryCode = node.categoryCode) === null || _node_categoryCode === void 0 ? void 0 : _node_categoryCode.toLowerCase().includes(lowerSearchTerm);\n            const isCurrentMatch = nameMatch || englishNameMatch || codeMatch;\n            // 递归搜索子节点\n            const matchingChildren = [];\n            if (node.children) {\n                node.children.forEach((child)=>{\n                    const childResult = searchInNode(child);\n                    if (childResult) {\n                        matchingChildren.push(childResult);\n                    }\n                });\n            }\n            // 如果当前节点匹配或有匹配的子节点，则返回节点\n            if (isCurrentMatch || matchingChildren.length > 0) {\n                return {\n                    ...node,\n                    children: matchingChildren.length > 0 ? matchingChildren : node.children\n                };\n            }\n            return null;\n        };\n        nodes.forEach((node)=>{\n            const result = searchInNode(node);\n            if (result) {\n                results.push(result);\n            }\n        });\n        return results;\n    };\n    // 获取搜索结果中所有匹配节点的路径（用于自动展开）\n    const getMatchingNodePaths = (nodes, searchTerm)=>{\n        if (!searchTerm.trim()) return [];\n        const paths = [];\n        const lowerSearchTerm = searchTerm.toLowerCase();\n        const findPaths = function(nodeList) {\n            let currentPath = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n            nodeList.forEach((node)=>{\n                var _node_categoryCode;\n                const newPath = [\n                    ...currentPath,\n                    node.id\n                ];\n                // 检查当前节点是否匹配\n                const nameMatch = node.name.toLowerCase().includes(lowerSearchTerm);\n                const englishNameMatch = node.englishName.toLowerCase().includes(lowerSearchTerm);\n                const codeMatch = (_node_categoryCode = node.categoryCode) === null || _node_categoryCode === void 0 ? void 0 : _node_categoryCode.toLowerCase().includes(lowerSearchTerm);\n                if (nameMatch || englishNameMatch || codeMatch) {\n                    // 添加到匹配节点的所有父节点路径\n                    newPath.slice(0, -1).forEach((parentId)=>{\n                        if (!paths.includes(parentId)) {\n                            paths.push(parentId);\n                        }\n                    });\n                }\n                // 递归搜索子节点\n                if (node.children && node.children.length > 0) {\n                    findPaths(node.children, newPath);\n                }\n            });\n        };\n        findPaths(nodes);\n        return paths;\n    };\n    // 处理搜索\n    const handleSearch = ()=>{\n        if (!searchValue.trim()) {\n            // 如果搜索框为空，重置展开状态\n            setExpandedNodes([\n                \"1\",\n                \"2\"\n            ]) // 恢复默认展开状态\n            ;\n            return;\n        }\n        // 获取匹配节点的父节点路径并自动展开\n        const pathsToExpand = getMatchingNodePaths(catalogTree, searchValue);\n        setExpandedNodes(pathsToExpand);\n    };\n    // 重置搜索\n    const handleResetSearch = ()=>{\n        setSearchValue(\"\");\n    };\n    // 过滤后的目录树（用于显示搜索结果）\n    const filteredCatalogTree = searchValue.trim() ? searchNodes(catalogTree, searchValue) : catalogTree;\n    const handleAdd = ()=>{\n        setIsAdding(true);\n        setIsEditing(false);\n        setSelectedNode(null);\n        setSelectedNodeLevel(0) // 一级分类的层级为0\n        ;\n        setShowEditDialog(true);\n        setFormData({\n            name: \"\",\n            englishName: \"\",\n            categoryCode: \"\",\n            description: \"\",\n            tags: [],\n            status: \"active\"\n        });\n    };\n    const handleEditNode = (node)=>{\n        setSelectedNode(node);\n        setIsEditing(true);\n        setIsAdding(false);\n        setShowEditDialog(true);\n        setFormData({\n            name: node.name,\n            englishName: node.englishName,\n            categoryCode: node.categoryCode,\n            description: node.description,\n            tags: node.tags,\n            status: node.status\n        });\n    };\n    const handleDeleteNode = async (node)=>{\n        const confirmed = await confirm({\n            title: \"删除分类\",\n            description: '确定要删除分类\"'.concat(node.name, '\"吗？此操作不可撤销。'),\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (confirmed) {\n            try {\n                await deleteCategory(parseInt(node.id));\n                refetchCategories();\n                // 显示删除成功提示\n                toast({\n                    title: \"删除成功\"\n                });\n            } catch (error) {\n                console.error(\"删除失败:\", error);\n                // 显示删除失败提示\n                toast({\n                    title: \"删除失败\",\n                    description: error instanceof Error ? error.message : \"删除时发生未知错误\",\n                    variant: \"destructive\"\n                });\n            }\n        }\n    };\n    const handleAddChild = (parentNode, parentLevel)=>{\n        setIsAdding(true);\n        setIsEditing(false);\n        setSelectedNode(parentNode);\n        setSelectedNodeLevel(parentLevel) // 存储父节点的层级\n        ;\n        setShowEditDialog(true);\n        setFormData({\n            name: \"\",\n            englishName: \"\",\n            categoryCode: \"\",\n            description: \"\",\n            tags: [],\n            autoSku: false,\n            status: \"active\"\n        });\n    };\n    const handleSave = async ()=>{\n        try {\n            var _formData_name, _formData_englishName;\n            // 验证必填字段\n            if (!((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim())) {\n                toast({\n                    title: \"验证失败\",\n                    description: \"请输入中文名称\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (!((_formData_englishName = formData.englishName) === null || _formData_englishName === void 0 ? void 0 : _formData_englishName.trim())) {\n                toast({\n                    title: \"验证失败\",\n                    description: \"请输入英文名称\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (isAdding) {\n                var _formData_categoryCode, _formData_description;\n                // 创建新分类\n                const categoryCode = (_formData_categoryCode = formData.categoryCode) === null || _formData_categoryCode === void 0 ? void 0 : _formData_categoryCode.trim();\n                // 根据是否有选中的父节点来确定层级和父ID\n                let categoryLevel = 1;\n                let parentId = null;\n                if (selectedNode) {\n                    // 如果有选中的父节点，说明是添加子分类\n                    parentId = parseInt(selectedNode.id);\n                    // 根据父节点的层级确定子节点层级\n                    // selectedNodeLevel: 0=一级, 1=二级, 2=三级, 3=四级, 4=五级, 5=六级 (前端显示层级)\n                    // category_level: 1=一级, 2=二级, 3=三级, 4=四级, 5=五级, 6=六级, 7=七级 (后端数据库层级)\n                    // 子分类的层级 = 父分类的数据库层级 + 1 = (selectedNodeLevel + 1) + 1\n                    categoryLevel = selectedNodeLevel + 2;\n                }\n                const createData = {\n                    // 分类编码处理：如果有值则转换为大写，如果为空则不传递该字段\n                    ...categoryCode ? {\n                        category_code: categoryCode.toUpperCase()\n                    } : {},\n                    chinese_name: formData.name.trim(),\n                    english_name: formData.englishName.trim(),\n                    status: formData.status === \"active\" ? \"enabled\" : \"disabled\",\n                    auto_sku: formData.autoSku ? \"enabled\" : \"disabled\",\n                    category_level: categoryLevel,\n                    parent_id: parentId,\n                    category_description: ((_formData_description = formData.description) === null || _formData_description === void 0 ? void 0 : _formData_description.trim()) || \"\",\n                    attribute_tags: formData.tags || [],\n                    sort_order: 0\n                };\n                await createCategory(createData);\n                setIsAdding(false);\n                setShowEditDialog(false);\n                setFormData({}) // 清空表单数据\n                ;\n                await refetchCategories() // 等待刷新完成\n                ;\n                // 显示成功提示\n                toast({\n                    title: \"创建成功\"\n                });\n            } else if (selectedNode && isEditing) {\n                var _formData_description1;\n                // 更新分类 - 编辑时不允许修改分类编码\n                const updateData = {\n                    chinese_name: formData.name.trim(),\n                    english_name: formData.englishName.trim(),\n                    status: formData.status === \"active\" ? \"enabled\" : \"disabled\",\n                    auto_sku: formData.autoSku ? \"enabled\" : \"disabled\",\n                    category_description: ((_formData_description1 = formData.description) === null || _formData_description1 === void 0 ? void 0 : _formData_description1.trim()) || \"\",\n                    attribute_tags: formData.tags || []\n                };\n                await updateCategory(parseInt(selectedNode.id), updateData);\n                setIsEditing(false);\n                setShowEditDialog(false);\n                await refetchCategories() // 等待刷新完成\n                ;\n                // 显示成功提示\n                toast({\n                    title: \"更新成功\"\n                });\n            }\n        } catch (error) {\n            console.error(\"保存失败:\", error);\n            // 显示错误提示\n            toast({\n                title: \"保存失败\",\n                description: error instanceof Error ? error.message : \"保存时发生未知错误\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleCancel = ()=>{\n        setIsEditing(false);\n        setIsAdding(false);\n        setShowEditDialog(false);\n        setFormData({});\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            placeholder: \"搜索目录\",\n                                            value: searchValue,\n                                            onChange: (e)=>{\n                                                setSearchValue(e.target.value);\n                                                // 实时搜索：输入时自动触发搜索\n                                                if (e.target.value.trim()) {\n                                                    const pathsToExpand = getMatchingNodePaths(catalogTree, e.target.value);\n                                                    setExpandedNodes(pathsToExpand);\n                                                } else {\n                                                    setExpandedNodes([\n                                                        \"1\",\n                                                        \"2\"\n                                                    ]) // 恢复默认展开状态\n                                                    ;\n                                                }\n                                            },\n                                            onKeyDown: (e)=>{\n                                                if (e.key === \"Enter\") {\n                                                    handleSearch();\n                                                }\n                                            },\n                                            className: \"w-80\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: handleResetSearch,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"重置\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleExpandAll,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"展开全部\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleCollapseAll,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"折叠全部\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        onClick: handleAdd,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"新增一级产品目录\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 418,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0\",\n                    children: categoriesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-muted-foreground\",\n                                children: \"加载中...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 13\n                    }, this) : categoriesError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm mb-2\",\n                                children: [\n                                    \"加载失败: \",\n                                    categoriesError\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: refetchCategories,\n                                children: \"重试\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm border-separate border-spacing-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"bg-muted/30 border-b\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"名称 / 英文名\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-32 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"描述\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-20 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"SKU数量\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"分类编码\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"报关编码\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"属性栏目\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-32 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"更新时间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground\",\n                                                children: \"操作\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: filteredCatalogTree.length > 0 ? filteredCatalogTree.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableTreeNode, {\n                                            node: node,\n                                            level: 0,\n                                            expandedNodes: expandedNodes,\n                                            onToggleExpanded: toggleExpanded,\n                                            onEdit: handleEditNode,\n                                            onDelete: handleDeleteNode,\n                                            onAddChild: handleAddChild,\n                                            searchTerm: searchValue\n                                        }, node.id, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 23\n                                        }, this)) : searchValue.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: 8,\n                                            className: \"p-8 text-center text-muted-foreground\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-8 h-8 text-muted-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"未找到匹配的目录\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: \"请尝试其他关键词或检查拼写\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: 8,\n                                            className: \"p-8 text-center text-muted-foreground\",\n                                            children: \"暂无数据\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 468,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showEditDialog,\n                onOpenChange: setShowEditDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"max-w-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                children: isAdding ? selectedNode ? \"添加\".concat([\n                                    \"二\",\n                                    \"三\",\n                                    \"四\",\n                                    \"五\",\n                                    \"六\",\n                                    \"七\"\n                                ][selectedNodeLevel], \"级产品目录\") : \"添加一级产品目录\" : \"编辑产品目录\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CatalogForm, {\n                            formData: formData,\n                            setFormData: setFormData,\n                            isEditing: true,\n                            isEditingExisting: !isAdding,\n                            onSave: handleSave,\n                            onCancel: handleCancel,\n                            loading: operationLoading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 538,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 537,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n        lineNumber: 416,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCatalogPage, \"90os8Ia1Am+c2vsT027XTEd1NRM=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_11__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast,\n        _hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategoryTree,\n        _hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategories\n    ];\n});\n_c = ProductCatalogPage;\n// 表格树节点组件\nfunction TableTreeNode(param) {\n    let { node, level, expandedNodes, onToggleExpanded, onEdit, onDelete, onAddChild, searchTerm = \"\" } = param;\n    var _node_children;\n    const hasChildren = node.children && node.children.length > 0;\n    const isExpanded = expandedNodes.includes(node.id);\n    // 高亮显示匹配的文本\n    const highlightText = (text, searchTerm)=>{\n        if (!searchTerm.trim()) return text;\n        const regex = new RegExp(\"(\".concat(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \")\"), \"gi\");\n        const parts = text.split(regex);\n        return parts.map((part, index)=>regex.test(part) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n                className: \"bg-yellow-200 text-yellow-900 px-1 rounded\",\n                children: part\n            }, index, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 597,\n                columnNumber: 9\n            }, this) : part);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            style: {\n                                paddingLeft: \"\".concat(level * 20, \"px\")\n                            },\n                            children: [\n                                hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>onToggleExpanded(node.id),\n                                    className: \"p-0.5 hover:bg-muted rounded\",\n                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-foreground\",\n                                            children: highlightText(node.name, searchTerm)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: highlightText(node.englishName, searchTerm)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 607,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground truncate max-w-32\",\n                            title: node.description,\n                            children: node.description || \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 634,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 638,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: \"outline\",\n                            className: \"text-xs\",\n                            children: node.categoryCode ? highlightText(node.categoryCode, searchTerm) : \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 642,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-1\",\n                            children: node.tags.length > 0 ? node.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"text-xs\",\n                                    children: tag\n                                }, index, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 17\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"--\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 650,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 649,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: node.createdAt\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 663,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 662,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onEdit(node),\n                                    className: \"h-7 px-2 text-xs text-blue-600 hover:text-blue-800\",\n                                    children: \"编辑\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 13\n                                }, this),\n                                level < 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onAddChild(node, level),\n                                    className: \"h-7 px-2 text-xs text-green-600 hover:text-green-800\",\n                                    children: [\n                                        \"新增\",\n                                        [\n                                            \"二\",\n                                            \"三\",\n                                            \"四\",\n                                            \"五\",\n                                            \"六\",\n                                            \"七\"\n                                        ][level],\n                                        \"级目录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onDelete(node),\n                                    className: \"h-7 px-2 text-xs text-red-600 hover:text-red-800\",\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 667,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 606,\n                columnNumber: 7\n            }, this),\n            hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: (_node_children = node.children) === null || _node_children === void 0 ? void 0 : _node_children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableTreeNode, {\n                        node: child,\n                        level: level + 1,\n                        expandedNodes: expandedNodes,\n                        onToggleExpanded: onToggleExpanded,\n                        onEdit: onEdit,\n                        onDelete: onDelete,\n                        onAddChild: onAddChild,\n                        searchTerm: searchTerm\n                    }, child.id, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 702,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false)\n        ]\n    }, void 0, true);\n}\n_c1 = TableTreeNode;\n// 分类表单组件\nfunction CatalogForm(param) {\n    let { formData, setFormData, isEditing, isEditingExisting = false, onSave, onCancel, loading } = param;\n    const availableTags = [\n        \"color\",\n        \"size\",\n        \"model\",\n        \"material\",\n        \"style\"\n    ];\n    const updateFormData = (field, value)=>{\n        setFormData({\n            ...formData,\n            [field]: value\n        });\n    };\n    if (isEditing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"name\",\n                                    children: [\n                                        \"中文名称 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 40\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 749,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"name\",\n                                    value: formData.name || \"\",\n                                    onChange: (e)=>updateFormData(\"name\", e.target.value),\n                                    placeholder: \"请输入中文名称\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 750,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 748,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"englishName\",\n                                    children: [\n                                        \"英文名称 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 47\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"englishName\",\n                                    value: formData.englishName || \"\",\n                                    onChange: (e)=>updateFormData(\"englishName\", e.target.value),\n                                    placeholder: \"请输入英文名称\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 758,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 747,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"categoryCode\",\n                                    children: [\n                                        \"分类编码 \",\n                                        isEditingExisting ? \"(不可修改)\" : \"(可选)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 772,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"categoryCode\",\n                                    value: formData.categoryCode || \"\",\n                                    onChange: (e)=>{\n                                        if (!isEditingExisting) {\n                                            // 只有在创建新分类时才允许修改\n                                            const value = e.target.value.toUpperCase().replace(/[^A-Z0-9_-]/g, \"\");\n                                            updateFormData(\"categoryCode\", value);\n                                        }\n                                    },\n                                    placeholder: isEditingExisting ? \"分类编码创建后不可修改\" : \"如：CLOTHING\",\n                                    disabled: isEditingExisting,\n                                    className: isEditingExisting ? \"bg-muted cursor-not-allowed\" : \"\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: isEditingExisting ? \"分类编码在创建后不能修改，以确保数据一致性\" : \"用于自动生成SKU，支持字母数字下划线连字符，会自动转换为大写\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 789,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 771,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"status\",\n                                    children: \"状态\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                    value: formData.status || \"active\",\n                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                placeholder: \"选择状态\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 803,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 802,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"active\",\n                                                    children: \"启用\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"inactive\",\n                                                    children: \"禁用\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 807,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 770,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                            htmlFor: \"description\",\n                            children: \"描述\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 814,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                            id: \"description\",\n                            value: formData.description || \"\",\n                            onChange: (e)=>updateFormData(\"description\", e.target.value),\n                            placeholder: \"请输入目录描述\",\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 815,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 813,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                            htmlFor: \"tags\",\n                            children: \"属性标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                            id: \"tags\",\n                            value: Array.isArray(formData.tags) ? formData.tags.join(\", \") : \"\",\n                            onChange: (e)=>updateFormData(\"tags\", e.target.value.split(\",\").map((t)=>t.trim()).filter((t)=>t)),\n                            placeholder: \"color, size\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 826,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: [\n                                \"可选标签: \",\n                                availableTags.join(\", \")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 832,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 824,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                            id: \"autoSku\",\n                            checked: !!formData.autoSku,\n                            onCheckedChange: (checked)=>updateFormData(\"autoSku\", !!checked)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 838,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                            htmlFor: \"autoSku\",\n                            children: \"自动生成SKU\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 843,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 837,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-end gap-2 pt-4 border-t\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            onClick: onCancel,\n                            disabled: loading,\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 848,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            onClick: onSave,\n                            disabled: loading,\n                            children: loading ? \"保存中...\" : \"确定\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 856,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 847,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n            lineNumber: 746,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n_c2 = CatalogForm;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ProductCatalogPage\");\n$RefreshReg$(_c1, \"TableTreeNode\");\n$RefreshReg$(_c2, \"CatalogForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/products/product-catalog-page.tsx\n"));

/***/ })

});