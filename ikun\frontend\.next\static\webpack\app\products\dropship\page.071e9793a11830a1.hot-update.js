"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/dropship/page",{

/***/ "(app-pages-browser)/./src/hooks/useCategories.ts":
/*!************************************!*\
  !*** ./src/hooks/useCategories.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCategories: function() { return /* binding */ useCategories; },\n/* harmony export */   useCategoriesByLevel: function() { return /* binding */ useCategoriesByLevel; },\n/* harmony export */   useCategory: function() { return /* binding */ useCategory; },\n/* harmony export */   useCategoryChildren: function() { return /* binding */ useCategoryChildren; },\n/* harmony export */   useCategoryTree: function() { return /* binding */ useCategoryTree; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/**\n * Categories Hooks\n * 分类相关的React Hooks\n */ \n\n// 分类列表Hook\nfunction useCategories(params) {\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        total: 0,\n        page: 1,\n        limit: 20,\n        totalPages: 0\n    });\n    const fetchCategories = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (searchParams)=>{\n        // 防止重复请求\n        if (loading) return;\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.categoriesApi.getCategories({\n                page: 1,\n                limit: 100,\n                ...params,\n                ...searchParams\n            });\n            setCategories(response.items);\n            setPagination({\n                total: response.total,\n                page: response.page,\n                limit: response.limit,\n                totalPages: response.totalPages\n            });\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"获取分类列表失败\");\n            setCategories([]);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        params\n    ]); // 移除loading依赖项，避免无限循环\n    // 创建分类\n    const createCategory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (categoryData)=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const newCategory = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.categoriesApi.createCategory(categoryData);\n            setCategories((prev)=>[\n                    newCategory,\n                    ...prev\n                ]);\n            return newCategory;\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"创建分类失败\");\n            throw err;\n        } finally{\n            setLoading(false);\n        }\n    }, []);\n    // 更新分类\n    const updateCategory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (id, categoryData)=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const updatedCategory = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.categoriesApi.updateCategory(id, categoryData);\n            setCategories((prev)=>prev.map((c)=>c.id === id ? updatedCategory : c));\n            return updatedCategory;\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"更新分类失败\");\n            throw err;\n        } finally{\n            setLoading(false);\n        }\n    }, []);\n    // 删除分类\n    const deleteCategory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (id)=>{\n        setLoading(true);\n        setError(null);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_1__.categoriesApi.deleteCategory(id);\n            setCategories((prev)=>prev.filter((c)=>c.id !== id));\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"删除分类失败\");\n            throw err;\n        } finally{\n            setLoading(false);\n        }\n    }, []);\n    // 更新分类状态\n    const updateCategoryStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (id, status)=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const updatedCategory = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.categoriesApi.updateCategoryStatus(id, status);\n            setCategories((prev)=>prev.map((c)=>c.id === id ? updatedCategory : c));\n            return updatedCategory;\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"更新分类状态失败\");\n            throw err;\n        } finally{\n            setLoading(false);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // 只有在 autoFetch 为 true（默认）时才自动获取数据\n        if ((params === null || params === void 0 ? void 0 : params.autoFetch) !== false) {\n            fetchCategories();\n        }\n    }, [\n        fetchCategories,\n        params === null || params === void 0 ? void 0 : params.autoFetch\n    ]);\n    return {\n        categories,\n        loading,\n        error,\n        pagination,\n        fetchCategories,\n        createCategory,\n        updateCategory,\n        deleteCategory,\n        updateCategoryStatus,\n        refetch: fetchCategories\n    };\n}\n// 分类树Hook\nfunction useCategoryTree() {\n    const [categoryTree, setCategoryTree] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const isRequestingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const hasInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const fetchCategoryTree = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        // 防止重复请求\n        if (isRequestingRef.current) {\n            return;\n        }\n        isRequestingRef.current = true;\n        setLoading(true);\n        setError(null);\n        try {\n            const tree = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.categoriesApi.getCategoryTree();\n            setCategoryTree(tree);\n            hasInitializedRef.current = true;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"获取分类树失败\";\n            setError(errorMessage);\n            setCategoryTree([]);\n        } finally{\n            setLoading(false);\n            isRequestingRef.current = false;\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // 只在未初始化时执行一次\n        if (!hasInitializedRef.current && !isRequestingRef.current) {\n            fetchCategoryTree();\n        }\n    }, []); // 空依赖数组，只在组件挂载时执行一次\n    return {\n        categoryTree,\n        loading,\n        error,\n        refetch: fetchCategoryTree\n    };\n}\n// 指定层级分类Hook\nfunction useCategoriesByLevel(level) {\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchCategoriesByLevel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (categoryLevel)=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const categoriesData = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.categoriesApi.getCategoriesByLevel(categoryLevel);\n            setCategories(categoriesData);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"获取指定层级分类失败\");\n            setCategories([]);\n        } finally{\n            setLoading(false);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (level) {\n            fetchCategoriesByLevel(level);\n        }\n    }, [\n        level,\n        fetchCategoriesByLevel\n    ]);\n    return {\n        categories,\n        loading,\n        error,\n        refetch: ()=>fetchCategoriesByLevel(level)\n    };\n}\n// 单个分类Hook\nfunction useCategory(id) {\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchCategory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (categoryId)=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const categoryData = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.categoriesApi.getCategory(categoryId);\n            setCategory(categoryData);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"获取分类详情失败\");\n            setCategory(null);\n        } finally{\n            setLoading(false);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (id) {\n            fetchCategory(id);\n        }\n    }, [\n        id,\n        fetchCategory\n    ]);\n    return {\n        category,\n        loading,\n        error,\n        refetch: id ? ()=>fetchCategory(id) : undefined\n    };\n}\n// 子分类Hook\nfunction useCategoryChildren(parentId) {\n    const [children, setChildren] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (id)=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const childrenData = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.categoriesApi.getCategoryChildren(id);\n            setChildren(childrenData);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"获取子分类失败\");\n            setChildren([]);\n        } finally{\n            setLoading(false);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (parentId) {\n            fetchChildren(parentId);\n        }\n    }, [\n        parentId,\n        fetchChildren\n    ]);\n    return {\n        children,\n        loading,\n        error,\n        refetch: parentId ? ()=>fetchChildren(parentId) : undefined\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VDYXRlZ29yaWVzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUE7OztDQUdDLEdBRWdFO0FBQ3ZCO0FBRzFDLFdBQVc7QUFDSixTQUFTSyxjQUFjQyxNQUs3QjtJQUNDLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHUiwrQ0FBUUEsQ0FBb0IsRUFBRTtJQUNsRSxNQUFNLENBQUNTLFNBQVNDLFdBQVcsR0FBR1YsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDVyxPQUFPQyxTQUFTLEdBQUdaLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNhLFlBQVlDLGNBQWMsR0FBR2QsK0NBQVFBLENBQUM7UUFDM0NlLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLFlBQVk7SUFDZDtJQUVBLE1BQU1DLGtCQUFrQmpCLGtEQUFXQSxDQUFDLE9BQU9rQjtRQUN6QyxTQUFTO1FBQ1QsSUFBSVgsU0FBUztRQUViQyxXQUFXO1FBQ1hFLFNBQVM7UUFFVCxJQUFJO1lBQ0YsTUFBTVMsV0FBVyxNQUFNakIsbURBQWFBLENBQUNrQixhQUFhLENBQUM7Z0JBQ2pETixNQUFNO2dCQUNOQyxPQUFPO2dCQUNQLEdBQUdYLE1BQU07Z0JBQ1QsR0FBR2MsWUFBWTtZQUNqQjtZQUVBWixjQUFjYSxTQUFTRSxLQUFLO1lBQzVCVCxjQUFjO2dCQUNaQyxPQUFPTSxTQUFTTixLQUFLO2dCQUNyQkMsTUFBTUssU0FBU0wsSUFBSTtnQkFDbkJDLE9BQU9JLFNBQVNKLEtBQUs7Z0JBQ3JCQyxZQUFZRyxTQUFTSCxVQUFVO1lBQ2pDO1FBQ0YsRUFBRSxPQUFPTSxLQUFLO1lBQ1paLFNBQVNZLGVBQWVDLFFBQVFELElBQUlFLE9BQU8sR0FBRztZQUM5Q2xCLGNBQWMsRUFBRTtRQUNsQixTQUFVO1lBQ1JFLFdBQVc7UUFDYjtJQUNGLEdBQUc7UUFBQ0o7S0FBTyxHQUFHLHNCQUFzQjtJQUVwQyxPQUFPO0lBQ1AsTUFBTXFCLGlCQUFpQnpCLGtEQUFXQSxDQUFDLE9BQU8wQjtRQUN4Q2xCLFdBQVc7UUFDWEUsU0FBUztRQUVULElBQUk7WUFDRixNQUFNaUIsY0FBYyxNQUFNekIsbURBQWFBLENBQUN1QixjQUFjLENBQUNDO1lBQ3ZEcEIsY0FBY3NCLENBQUFBLE9BQVE7b0JBQUNEO3VCQUFnQkM7aUJBQUs7WUFDNUMsT0FBT0Q7UUFDVCxFQUFFLE9BQU9MLEtBQUs7WUFDWlosU0FBU1ksZUFBZUMsUUFBUUQsSUFBSUUsT0FBTyxHQUFHO1lBQzlDLE1BQU1GO1FBQ1IsU0FBVTtZQUNSZCxXQUFXO1FBQ2I7SUFDRixHQUFHLEVBQUU7SUFFTCxPQUFPO0lBQ1AsTUFBTXFCLGlCQUFpQjdCLGtEQUFXQSxDQUFDLE9BQU84QixJQUFZSjtRQUNwRGxCLFdBQVc7UUFDWEUsU0FBUztRQUVULElBQUk7WUFDRixNQUFNcUIsa0JBQWtCLE1BQU03QixtREFBYUEsQ0FBQzJCLGNBQWMsQ0FBQ0MsSUFBSUo7WUFDL0RwQixjQUFjc0IsQ0FBQUEsT0FBUUEsS0FBS0ksR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFSCxFQUFFLEtBQUtBLEtBQUtDLGtCQUFrQkU7WUFDcEUsT0FBT0Y7UUFDVCxFQUFFLE9BQU9ULEtBQUs7WUFDWlosU0FBU1ksZUFBZUMsUUFBUUQsSUFBSUUsT0FBTyxHQUFHO1lBQzlDLE1BQU1GO1FBQ1IsU0FBVTtZQUNSZCxXQUFXO1FBQ2I7SUFDRixHQUFHLEVBQUU7SUFFTCxPQUFPO0lBQ1AsTUFBTTBCLGlCQUFpQmxDLGtEQUFXQSxDQUFDLE9BQU84QjtRQUN4Q3RCLFdBQVc7UUFDWEUsU0FBUztRQUVULElBQUk7WUFDRixNQUFNUixtREFBYUEsQ0FBQ2dDLGNBQWMsQ0FBQ0o7WUFDbkN4QixjQUFjc0IsQ0FBQUEsT0FBUUEsS0FBS08sTUFBTSxDQUFDRixDQUFBQSxJQUFLQSxFQUFFSCxFQUFFLEtBQUtBO1FBQ2xELEVBQUUsT0FBT1IsS0FBSztZQUNaWixTQUFTWSxlQUFlQyxRQUFRRCxJQUFJRSxPQUFPLEdBQUc7WUFDOUMsTUFBTUY7UUFDUixTQUFVO1lBQ1JkLFdBQVc7UUFDYjtJQUNGLEdBQUcsRUFBRTtJQUVMLFNBQVM7SUFDVCxNQUFNNEIsdUJBQXVCcEMsa0RBQVdBLENBQUMsT0FBTzhCLElBQVlPO1FBQzFEN0IsV0FBVztRQUNYRSxTQUFTO1FBRVQsSUFBSTtZQUNGLE1BQU1xQixrQkFBa0IsTUFBTTdCLG1EQUFhQSxDQUFDa0Msb0JBQW9CLENBQUNOLElBQUlPO1lBQ3JFL0IsY0FBY3NCLENBQUFBLE9BQVFBLEtBQUtJLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUgsRUFBRSxLQUFLQSxLQUFLQyxrQkFBa0JFO1lBQ3BFLE9BQU9GO1FBQ1QsRUFBRSxPQUFPVCxLQUFLO1lBQ1paLFNBQVNZLGVBQWVDLFFBQVFELElBQUlFLE9BQU8sR0FBRztZQUM5QyxNQUFNRjtRQUNSLFNBQVU7WUFDUmQsV0FBVztRQUNiO0lBQ0YsR0FBRyxFQUFFO0lBRUxULGdEQUFTQSxDQUFDO1FBQ1IsbUNBQW1DO1FBQ25DLElBQUlLLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUWtDLFNBQVMsTUFBSyxPQUFPO1lBQy9CckI7UUFDRjtJQUNGLEdBQUc7UUFBQ0E7UUFBaUJiLG1CQUFBQSw2QkFBQUEsT0FBUWtDLFNBQVM7S0FBQztJQUV2QyxPQUFPO1FBQ0xqQztRQUNBRTtRQUNBRTtRQUNBRTtRQUNBTTtRQUNBUTtRQUNBSTtRQUNBSztRQUNBRTtRQUNBRyxTQUFTdEI7SUFDWDtBQUNGO0FBRUEsVUFBVTtBQUNILFNBQVN1QjtJQUNkLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUc1QywrQ0FBUUEsQ0FBb0IsRUFBRTtJQUN0RSxNQUFNLENBQUNTLFNBQVNDLFdBQVcsR0FBR1YsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDVyxPQUFPQyxTQUFTLEdBQUdaLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNNkMsa0JBQWtCMUMsNkNBQU1BLENBQUM7SUFDL0IsTUFBTTJDLG9CQUFvQjNDLDZDQUFNQSxDQUFDO0lBRWpDLE1BQU00QyxvQkFBb0I3QyxrREFBV0EsQ0FBQztRQUNwQyxTQUFTO1FBQ1QsSUFBSTJDLGdCQUFnQkcsT0FBTyxFQUFFO1lBQzNCO1FBQ0Y7UUFFQUgsZ0JBQWdCRyxPQUFPLEdBQUc7UUFDMUJ0QyxXQUFXO1FBQ1hFLFNBQVM7UUFFVCxJQUFJO1lBQ0YsTUFBTXFDLE9BQU8sTUFBTTdDLG1EQUFhQSxDQUFDOEMsZUFBZTtZQUNoRE4sZ0JBQWdCSztZQUNoQkgsa0JBQWtCRSxPQUFPLEdBQUc7UUFDOUIsRUFBRSxPQUFPeEIsS0FBSztZQUNaLE1BQU0yQixlQUFlM0IsZUFBZUMsUUFBUUQsSUFBSUUsT0FBTyxHQUFHO1lBQzFEZCxTQUFTdUM7WUFDVFAsZ0JBQWdCLEVBQUU7UUFDcEIsU0FBVTtZQUNSbEMsV0FBVztZQUNYbUMsZ0JBQWdCRyxPQUFPLEdBQUc7UUFDNUI7SUFDRixHQUFHLEVBQUU7SUFFTC9DLGdEQUFTQSxDQUFDO1FBQ1IsY0FBYztRQUNkLElBQUksQ0FBQzZDLGtCQUFrQkUsT0FBTyxJQUFJLENBQUNILGdCQUFnQkcsT0FBTyxFQUFFO1lBQzFERDtRQUNGO0lBQ0YsR0FBRyxFQUFFLEdBQUcsb0JBQW9CO0lBRTVCLE9BQU87UUFDTEo7UUFDQWxDO1FBQ0FFO1FBQ0E4QixTQUFTTTtJQUNYO0FBQ0Y7QUFFQSxhQUFhO0FBQ04sU0FBU0sscUJBQXFCQyxLQUFhO0lBQ2hELE1BQU0sQ0FBQzlDLFlBQVlDLGNBQWMsR0FBR1IsK0NBQVFBLENBQW9CLEVBQUU7SUFDbEUsTUFBTSxDQUFDUyxTQUFTQyxXQUFXLEdBQUdWLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ1csT0FBT0MsU0FBUyxHQUFHWiwrQ0FBUUEsQ0FBZ0I7SUFFbEQsTUFBTXNELHlCQUF5QnBELGtEQUFXQSxDQUFDLE9BQU9xRDtRQUNoRDdDLFdBQVc7UUFDWEUsU0FBUztRQUVULElBQUk7WUFDRixNQUFNNEMsaUJBQWlCLE1BQU1wRCxtREFBYUEsQ0FBQ3FELG9CQUFvQixDQUFDRjtZQUNoRS9DLGNBQWNnRDtRQUNoQixFQUFFLE9BQU9oQyxLQUFLO1lBQ1paLFNBQVNZLGVBQWVDLFFBQVFELElBQUlFLE9BQU8sR0FBRztZQUM5Q2xCLGNBQWMsRUFBRTtRQUNsQixTQUFVO1lBQ1JFLFdBQVc7UUFDYjtJQUNGLEdBQUcsRUFBRTtJQUVMVCxnREFBU0EsQ0FBQztRQUNSLElBQUlvRCxPQUFPO1lBQ1RDLHVCQUF1QkQ7UUFDekI7SUFDRixHQUFHO1FBQUNBO1FBQU9DO0tBQXVCO0lBRWxDLE9BQU87UUFDTC9DO1FBQ0FFO1FBQ0FFO1FBQ0E4QixTQUFTLElBQU1hLHVCQUF1QkQ7SUFDeEM7QUFDRjtBQUVBLFdBQVc7QUFDSixTQUFTSyxZQUFZMUIsRUFBaUI7SUFDM0MsTUFBTSxDQUFDMkIsVUFBVUMsWUFBWSxHQUFHNUQsK0NBQVFBLENBQXlCO0lBQ2pFLE1BQU0sQ0FBQ1MsU0FBU0MsV0FBVyxHQUFHViwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNXLE9BQU9DLFNBQVMsR0FBR1osK0NBQVFBLENBQWdCO0lBRWxELE1BQU02RCxnQkFBZ0IzRCxrREFBV0EsQ0FBQyxPQUFPNEQ7UUFDdkNwRCxXQUFXO1FBQ1hFLFNBQVM7UUFFVCxJQUFJO1lBQ0YsTUFBTWdCLGVBQWUsTUFBTXhCLG1EQUFhQSxDQUFDMkQsV0FBVyxDQUFDRDtZQUNyREYsWUFBWWhDO1FBQ2QsRUFBRSxPQUFPSixLQUFLO1lBQ1paLFNBQVNZLGVBQWVDLFFBQVFELElBQUlFLE9BQU8sR0FBRztZQUM5Q2tDLFlBQVk7UUFDZCxTQUFVO1lBQ1JsRCxXQUFXO1FBQ2I7SUFDRixHQUFHLEVBQUU7SUFFTFQsZ0RBQVNBLENBQUM7UUFDUixJQUFJK0IsSUFBSTtZQUNONkIsY0FBYzdCO1FBQ2hCO0lBQ0YsR0FBRztRQUFDQTtRQUFJNkI7S0FBYztJQUV0QixPQUFPO1FBQ0xGO1FBQ0FsRDtRQUNBRTtRQUNBOEIsU0FBU1QsS0FBSyxJQUFNNkIsY0FBYzdCLE1BQU1nQztJQUMxQztBQUNGO0FBRUEsVUFBVTtBQUNILFNBQVNDLG9CQUFvQkMsUUFBdUI7SUFDekQsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdwRSwrQ0FBUUEsQ0FBb0IsRUFBRTtJQUM5RCxNQUFNLENBQUNTLFNBQVNDLFdBQVcsR0FBR1YsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDVyxPQUFPQyxTQUFTLEdBQUdaLCtDQUFRQSxDQUFnQjtJQUVsRCxNQUFNcUUsZ0JBQWdCbkUsa0RBQVdBLENBQUMsT0FBTzhCO1FBQ3ZDdEIsV0FBVztRQUNYRSxTQUFTO1FBRVQsSUFBSTtZQUNGLE1BQU0wRCxlQUFlLE1BQU1sRSxtREFBYUEsQ0FBQ21FLG1CQUFtQixDQUFDdkM7WUFDN0RvQyxZQUFZRTtRQUNkLEVBQUUsT0FBTzlDLEtBQUs7WUFDWlosU0FBU1ksZUFBZUMsUUFBUUQsSUFBSUUsT0FBTyxHQUFHO1lBQzlDMEMsWUFBWSxFQUFFO1FBQ2hCLFNBQVU7WUFDUjFELFdBQVc7UUFDYjtJQUNGLEdBQUcsRUFBRTtJQUVMVCxnREFBU0EsQ0FBQztRQUNSLElBQUlpRSxVQUFVO1lBQ1pHLGNBQWNIO1FBQ2hCO0lBQ0YsR0FBRztRQUFDQTtRQUFVRztLQUFjO0lBRTVCLE9BQU87UUFDTEY7UUFDQTFEO1FBQ0FFO1FBQ0E4QixTQUFTeUIsV0FBVyxJQUFNRyxjQUFjSCxZQUFZRjtJQUN0RDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9ob29rcy91c2VDYXRlZ29yaWVzLnRzPzY4M2MiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDYXRlZ29yaWVzIEhvb2tzXG4gKiDliIbnsbvnm7jlhbPnmoRSZWFjdCBIb29rc1xuICovXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrLCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBjYXRlZ29yaWVzQXBpIH0gZnJvbSAnQC9saWIvYXBpJztcbmltcG9ydCB7IFByb2R1Y3RDYXRlZ29yeSwgUGFnaW5hdGVkUmVzcG9uc2UgfSBmcm9tICdAL3R5cGVzJztcblxuLy8g5YiG57G75YiX6KGoSG9va1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUNhdGVnb3JpZXMocGFyYW1zPzoge1xuICBsZXZlbD86IG51bWJlcjtcbiAgcGFyZW50X2lkPzogbnVtYmVyO1xuICBzdGF0dXM/OiBzdHJpbmc7XG4gIGF1dG9GZXRjaD86IGJvb2xlYW47IC8vIOaYr+WQpuiHquWKqOiOt+WPluaVsOaNru+8jOm7mOiupOS4unRydWVcbn0pIHtcbiAgY29uc3QgW2NhdGVnb3JpZXMsIHNldENhdGVnb3JpZXNdID0gdXNlU3RhdGU8UHJvZHVjdENhdGVnb3J5W10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbcGFnaW5hdGlvbiwgc2V0UGFnaW5hdGlvbl0gPSB1c2VTdGF0ZSh7XG4gICAgdG90YWw6IDAsXG4gICAgcGFnZTogMSxcbiAgICBsaW1pdDogMjAsXG4gICAgdG90YWxQYWdlczogMFxuICB9KTtcblxuICBjb25zdCBmZXRjaENhdGVnb3JpZXMgPSB1c2VDYWxsYmFjayhhc3luYyAoc2VhcmNoUGFyYW1zPzogYW55KSA9PiB7XG4gICAgLy8g6Ziy5q2i6YeN5aSN6K+35rGCXG4gICAgaWYgKGxvYWRpbmcpIHJldHVybjtcblxuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjYXRlZ29yaWVzQXBpLmdldENhdGVnb3JpZXMoe1xuICAgICAgICBwYWdlOiAxLFxuICAgICAgICBsaW1pdDogMTAwLCAvLyDliIbnsbvpgJrluLjkuI3kvJrlpKrlpJrvvIzlj6/ku6XkuIDmrKHmgKfliqDovb3mm7TlpJpcbiAgICAgICAgLi4ucGFyYW1zLFxuICAgICAgICAuLi5zZWFyY2hQYXJhbXNcbiAgICAgIH0pO1xuXG4gICAgICBzZXRDYXRlZ29yaWVzKHJlc3BvbnNlLml0ZW1zKTtcbiAgICAgIHNldFBhZ2luYXRpb24oe1xuICAgICAgICB0b3RhbDogcmVzcG9uc2UudG90YWwsXG4gICAgICAgIHBhZ2U6IHJlc3BvbnNlLnBhZ2UsXG4gICAgICAgIGxpbWl0OiByZXNwb25zZS5saW1pdCxcbiAgICAgICAgdG90YWxQYWdlczogcmVzcG9uc2UudG90YWxQYWdlc1xuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ+iOt+WPluWIhuexu+WIl+ihqOWksei0pScpO1xuICAgICAgc2V0Q2F0ZWdvcmllcyhbXSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfSwgW3BhcmFtc10pOyAvLyDnp7vpmaRsb2FkaW5n5L6d6LWW6aG577yM6YG/5YWN5peg6ZmQ5b6q546vXG5cbiAgLy8g5Yib5bu65YiG57G7XG4gIGNvbnN0IGNyZWF0ZUNhdGVnb3J5ID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNhdGVnb3J5RGF0YTogUGFydGlhbDxQcm9kdWN0Q2F0ZWdvcnk+KSA9PiB7XG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgbmV3Q2F0ZWdvcnkgPSBhd2FpdCBjYXRlZ29yaWVzQXBpLmNyZWF0ZUNhdGVnb3J5KGNhdGVnb3J5RGF0YSk7XG4gICAgICBzZXRDYXRlZ29yaWVzKHByZXYgPT4gW25ld0NhdGVnb3J5LCAuLi5wcmV2XSk7XG4gICAgICByZXR1cm4gbmV3Q2F0ZWdvcnk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ+WIm+W7uuWIhuexu+Wksei0pScpO1xuICAgICAgdGhyb3cgZXJyO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICAvLyDmm7TmlrDliIbnsbtcbiAgY29uc3QgdXBkYXRlQ2F0ZWdvcnkgPSB1c2VDYWxsYmFjayhhc3luYyAoaWQ6IG51bWJlciwgY2F0ZWdvcnlEYXRhOiBQYXJ0aWFsPFByb2R1Y3RDYXRlZ29yeT4pID0+IHtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuICAgIFxuICAgIHRyeSB7XG4gICAgICBjb25zdCB1cGRhdGVkQ2F0ZWdvcnkgPSBhd2FpdCBjYXRlZ29yaWVzQXBpLnVwZGF0ZUNhdGVnb3J5KGlkLCBjYXRlZ29yeURhdGEpO1xuICAgICAgc2V0Q2F0ZWdvcmllcyhwcmV2ID0+IHByZXYubWFwKGMgPT4gYy5pZCA9PT0gaWQgPyB1cGRhdGVkQ2F0ZWdvcnkgOiBjKSk7XG4gICAgICByZXR1cm4gdXBkYXRlZENhdGVnb3J5O1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICfmm7TmlrDliIbnsbvlpLHotKUnKTtcbiAgICAgIHRocm93IGVycjtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8g5Yig6Zmk5YiG57G7XG4gIGNvbnN0IGRlbGV0ZUNhdGVnb3J5ID0gdXNlQ2FsbGJhY2soYXN5bmMgKGlkOiBudW1iZXIpID0+IHtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuICAgIFxuICAgIHRyeSB7XG4gICAgICBhd2FpdCBjYXRlZ29yaWVzQXBpLmRlbGV0ZUNhdGVnb3J5KGlkKTtcbiAgICAgIHNldENhdGVnb3JpZXMocHJldiA9PiBwcmV2LmZpbHRlcihjID0+IGMuaWQgIT09IGlkKSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ+WIoOmZpOWIhuexu+Wksei0pScpO1xuICAgICAgdGhyb3cgZXJyO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICAvLyDmm7TmlrDliIbnsbvnirbmgIFcbiAgY29uc3QgdXBkYXRlQ2F0ZWdvcnlTdGF0dXMgPSB1c2VDYWxsYmFjayhhc3luYyAoaWQ6IG51bWJlciwgc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuICAgIFxuICAgIHRyeSB7XG4gICAgICBjb25zdCB1cGRhdGVkQ2F0ZWdvcnkgPSBhd2FpdCBjYXRlZ29yaWVzQXBpLnVwZGF0ZUNhdGVnb3J5U3RhdHVzKGlkLCBzdGF0dXMpO1xuICAgICAgc2V0Q2F0ZWdvcmllcyhwcmV2ID0+IHByZXYubWFwKGMgPT4gYy5pZCA9PT0gaWQgPyB1cGRhdGVkQ2F0ZWdvcnkgOiBjKSk7XG4gICAgICByZXR1cm4gdXBkYXRlZENhdGVnb3J5O1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICfmm7TmlrDliIbnsbvnirbmgIHlpLHotKUnKTtcbiAgICAgIHRocm93IGVycjtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyDlj6rmnInlnKggYXV0b0ZldGNoIOS4uiB0cnVl77yI6buY6K6k77yJ5pe25omN6Ieq5Yqo6I635Y+W5pWw5o2uXG4gICAgaWYgKHBhcmFtcz8uYXV0b0ZldGNoICE9PSBmYWxzZSkge1xuICAgICAgZmV0Y2hDYXRlZ29yaWVzKCk7XG4gICAgfVxuICB9LCBbZmV0Y2hDYXRlZ29yaWVzLCBwYXJhbXM/LmF1dG9GZXRjaF0pO1xuXG4gIHJldHVybiB7XG4gICAgY2F0ZWdvcmllcyxcbiAgICBsb2FkaW5nLFxuICAgIGVycm9yLFxuICAgIHBhZ2luYXRpb24sXG4gICAgZmV0Y2hDYXRlZ29yaWVzLFxuICAgIGNyZWF0ZUNhdGVnb3J5LFxuICAgIHVwZGF0ZUNhdGVnb3J5LFxuICAgIGRlbGV0ZUNhdGVnb3J5LFxuICAgIHVwZGF0ZUNhdGVnb3J5U3RhdHVzLFxuICAgIHJlZmV0Y2g6IGZldGNoQ2F0ZWdvcmllc1xuICB9O1xufVxuXG4vLyDliIbnsbvmoJFIb29rXG5leHBvcnQgZnVuY3Rpb24gdXNlQ2F0ZWdvcnlUcmVlKCkge1xuICBjb25zdCBbY2F0ZWdvcnlUcmVlLCBzZXRDYXRlZ29yeVRyZWVdID0gdXNlU3RhdGU8UHJvZHVjdENhdGVnb3J5W10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBpc1JlcXVlc3RpbmdSZWYgPSB1c2VSZWYoZmFsc2UpO1xuICBjb25zdCBoYXNJbml0aWFsaXplZFJlZiA9IHVzZVJlZihmYWxzZSk7XG5cbiAgY29uc3QgZmV0Y2hDYXRlZ29yeVRyZWUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgLy8g6Ziy5q2i6YeN5aSN6K+35rGCXG4gICAgaWYgKGlzUmVxdWVzdGluZ1JlZi5jdXJyZW50KSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaXNSZXF1ZXN0aW5nUmVmLmN1cnJlbnQgPSB0cnVlO1xuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdHJlZSA9IGF3YWl0IGNhdGVnb3JpZXNBcGkuZ2V0Q2F0ZWdvcnlUcmVlKCk7XG4gICAgICBzZXRDYXRlZ29yeVRyZWUodHJlZSk7XG4gICAgICBoYXNJbml0aWFsaXplZFJlZi5jdXJyZW50ID0gdHJ1ZTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAn6I635Y+W5YiG57G75qCR5aSx6LSlJztcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICBzZXRDYXRlZ29yeVRyZWUoW10pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIGlzUmVxdWVzdGluZ1JlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyDlj6rlnKjmnKrliJ3lp4vljJbml7bmiafooYzkuIDmrKFcbiAgICBpZiAoIWhhc0luaXRpYWxpemVkUmVmLmN1cnJlbnQgJiYgIWlzUmVxdWVzdGluZ1JlZi5jdXJyZW50KSB7XG4gICAgICBmZXRjaENhdGVnb3J5VHJlZSgpO1xuICAgIH1cbiAgfSwgW10pOyAvLyDnqbrkvp3otZbmlbDnu4TvvIzlj6rlnKjnu4Tku7bmjILovb3ml7bmiafooYzkuIDmrKFcblxuICByZXR1cm4ge1xuICAgIGNhdGVnb3J5VHJlZSxcbiAgICBsb2FkaW5nLFxuICAgIGVycm9yLFxuICAgIHJlZmV0Y2g6IGZldGNoQ2F0ZWdvcnlUcmVlXG4gIH07XG59XG5cbi8vIOaMh+WumuWxgue6p+WIhuexu0hvb2tcbmV4cG9ydCBmdW5jdGlvbiB1c2VDYXRlZ29yaWVzQnlMZXZlbChsZXZlbDogbnVtYmVyKSB7XG4gIGNvbnN0IFtjYXRlZ29yaWVzLCBzZXRDYXRlZ29yaWVzXSA9IHVzZVN0YXRlPFByb2R1Y3RDYXRlZ29yeVtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcblxuICBjb25zdCBmZXRjaENhdGVnb3JpZXNCeUxldmVsID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNhdGVnb3J5TGV2ZWw6IG51bWJlcikgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNhdGVnb3JpZXNEYXRhID0gYXdhaXQgY2F0ZWdvcmllc0FwaS5nZXRDYXRlZ29yaWVzQnlMZXZlbChjYXRlZ29yeUxldmVsKTtcbiAgICAgIHNldENhdGVnb3JpZXMoY2F0ZWdvcmllc0RhdGEpO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICfojrflj5bmjIflrprlsYLnuqfliIbnsbvlpLHotKUnKTtcbiAgICAgIHNldENhdGVnb3JpZXMoW10pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChsZXZlbCkge1xuICAgICAgZmV0Y2hDYXRlZ29yaWVzQnlMZXZlbChsZXZlbCk7XG4gICAgfVxuICB9LCBbbGV2ZWwsIGZldGNoQ2F0ZWdvcmllc0J5TGV2ZWxdKTtcblxuICByZXR1cm4ge1xuICAgIGNhdGVnb3JpZXMsXG4gICAgbG9hZGluZyxcbiAgICBlcnJvcixcbiAgICByZWZldGNoOiAoKSA9PiBmZXRjaENhdGVnb3JpZXNCeUxldmVsKGxldmVsKVxuICB9O1xufVxuXG4vLyDljZXkuKrliIbnsbtIb29rXG5leHBvcnQgZnVuY3Rpb24gdXNlQ2F0ZWdvcnkoaWQ6IG51bWJlciB8IG51bGwpIHtcbiAgY29uc3QgW2NhdGVnb3J5LCBzZXRDYXRlZ29yeV0gPSB1c2VTdGF0ZTxQcm9kdWN0Q2F0ZWdvcnkgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gIGNvbnN0IGZldGNoQ2F0ZWdvcnkgPSB1c2VDYWxsYmFjayhhc3luYyAoY2F0ZWdvcnlJZDogbnVtYmVyKSA9PiB7XG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgY2F0ZWdvcnlEYXRhID0gYXdhaXQgY2F0ZWdvcmllc0FwaS5nZXRDYXRlZ29yeShjYXRlZ29yeUlkKTtcbiAgICAgIHNldENhdGVnb3J5KGNhdGVnb3J5RGF0YSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ+iOt+WPluWIhuexu+ivpuaDheWksei0pScpO1xuICAgICAgc2V0Q2F0ZWdvcnkobnVsbCk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlkKSB7XG4gICAgICBmZXRjaENhdGVnb3J5KGlkKTtcbiAgICB9XG4gIH0sIFtpZCwgZmV0Y2hDYXRlZ29yeV0pO1xuXG4gIHJldHVybiB7XG4gICAgY2F0ZWdvcnksXG4gICAgbG9hZGluZyxcbiAgICBlcnJvcixcbiAgICByZWZldGNoOiBpZCA/ICgpID0+IGZldGNoQ2F0ZWdvcnkoaWQpIDogdW5kZWZpbmVkXG4gIH07XG59XG5cbi8vIOWtkOWIhuexu0hvb2tcbmV4cG9ydCBmdW5jdGlvbiB1c2VDYXRlZ29yeUNoaWxkcmVuKHBhcmVudElkOiBudW1iZXIgfCBudWxsKSB7XG4gIGNvbnN0IFtjaGlsZHJlbiwgc2V0Q2hpbGRyZW5dID0gdXNlU3RhdGU8UHJvZHVjdENhdGVnb3J5W10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gIGNvbnN0IGZldGNoQ2hpbGRyZW4gPSB1c2VDYWxsYmFjayhhc3luYyAoaWQ6IG51bWJlcikgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNoaWxkcmVuRGF0YSA9IGF3YWl0IGNhdGVnb3JpZXNBcGkuZ2V0Q2F0ZWdvcnlDaGlsZHJlbihpZCk7XG4gICAgICBzZXRDaGlsZHJlbihjaGlsZHJlbkRhdGEpO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICfojrflj5blrZDliIbnsbvlpLHotKUnKTtcbiAgICAgIHNldENoaWxkcmVuKFtdKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAocGFyZW50SWQpIHtcbiAgICAgIGZldGNoQ2hpbGRyZW4ocGFyZW50SWQpO1xuICAgIH1cbiAgfSwgW3BhcmVudElkLCBmZXRjaENoaWxkcmVuXSk7XG5cbiAgcmV0dXJuIHtcbiAgICBjaGlsZHJlbixcbiAgICBsb2FkaW5nLFxuICAgIGVycm9yLFxuICAgIHJlZmV0Y2g6IHBhcmVudElkID8gKCkgPT4gZmV0Y2hDaGlsZHJlbihwYXJlbnRJZCkgOiB1bmRlZmluZWRcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwidXNlUmVmIiwiY2F0ZWdvcmllc0FwaSIsInVzZUNhdGVnb3JpZXMiLCJwYXJhbXMiLCJjYXRlZ29yaWVzIiwic2V0Q2F0ZWdvcmllcyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInBhZ2luYXRpb24iLCJzZXRQYWdpbmF0aW9uIiwidG90YWwiLCJwYWdlIiwibGltaXQiLCJ0b3RhbFBhZ2VzIiwiZmV0Y2hDYXRlZ29yaWVzIiwic2VhcmNoUGFyYW1zIiwicmVzcG9uc2UiLCJnZXRDYXRlZ29yaWVzIiwiaXRlbXMiLCJlcnIiLCJFcnJvciIsIm1lc3NhZ2UiLCJjcmVhdGVDYXRlZ29yeSIsImNhdGVnb3J5RGF0YSIsIm5ld0NhdGVnb3J5IiwicHJldiIsInVwZGF0ZUNhdGVnb3J5IiwiaWQiLCJ1cGRhdGVkQ2F0ZWdvcnkiLCJtYXAiLCJjIiwiZGVsZXRlQ2F0ZWdvcnkiLCJmaWx0ZXIiLCJ1cGRhdGVDYXRlZ29yeVN0YXR1cyIsInN0YXR1cyIsImF1dG9GZXRjaCIsInJlZmV0Y2giLCJ1c2VDYXRlZ29yeVRyZWUiLCJjYXRlZ29yeVRyZWUiLCJzZXRDYXRlZ29yeVRyZWUiLCJpc1JlcXVlc3RpbmdSZWYiLCJoYXNJbml0aWFsaXplZFJlZiIsImZldGNoQ2F0ZWdvcnlUcmVlIiwiY3VycmVudCIsInRyZWUiLCJnZXRDYXRlZ29yeVRyZWUiLCJlcnJvck1lc3NhZ2UiLCJ1c2VDYXRlZ29yaWVzQnlMZXZlbCIsImxldmVsIiwiZmV0Y2hDYXRlZ29yaWVzQnlMZXZlbCIsImNhdGVnb3J5TGV2ZWwiLCJjYXRlZ29yaWVzRGF0YSIsImdldENhdGVnb3JpZXNCeUxldmVsIiwidXNlQ2F0ZWdvcnkiLCJjYXRlZ29yeSIsInNldENhdGVnb3J5IiwiZmV0Y2hDYXRlZ29yeSIsImNhdGVnb3J5SWQiLCJnZXRDYXRlZ29yeSIsInVuZGVmaW5lZCIsInVzZUNhdGVnb3J5Q2hpbGRyZW4iLCJwYXJlbnRJZCIsImNoaWxkcmVuIiwic2V0Q2hpbGRyZW4iLCJmZXRjaENoaWxkcmVuIiwiY2hpbGRyZW5EYXRhIiwiZ2V0Q2F0ZWdvcnlDaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useCategories.ts\n"));

/***/ })

});