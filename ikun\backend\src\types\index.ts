/**
 * Type Definitions for IKUN ERP Backend
 */

// Database Types
export interface DatabaseRow {
  id: number;
  created_at: Date;
  updated_at: Date;
}

// Product Category Types
export interface ProductCategory extends DatabaseRow {
  category_code: string | null;
  chinese_name: string;
  english_name: string;
  status: 'enabled' | 'disabled';
  auto_sku: 'enabled' | 'disabled';
  category_level: 1 | 2 | 3 | 4 | 5 | 6 | 7;
  parent_id: number | null;
  category_description?: string;
  attribute_tags?: string[];
  category_path: string;
}

// Product Types
export interface DropshipProduct extends DatabaseRow {
  source: string;
  sku: string;
  ean: string;
  category_id: number;
  category: string;
  english_title: string;
  english_description: string;
  selling_point?: string[];
  image1: string;
  image2?: string;
  image3?: string;
  image4?: string;
  image5?: string;
  cost_price?: number;
  package_weight?: number;
  package_length?: number;
  package_width?: number;
  package_height?: number;
  purchase_link?: string;
  claim_time?: Date;
  claim_platform?: string;
  listing_count: number;
  remarks?: string;
  status: 'draft' | 'active' | 'inactive';
}

// Order Types
export interface Order extends DatabaseRow {
  order_number: string;           // 订单编号，唯一不可修改
  transaction_id: string;         // 交易编号，唯一不可修改
  store_name: string;             // 店铺名（店铺别名）
  platform: string;              // 所属平台
  payment_time?: Date;            // 付款时间（北京时间）
  platform_order_status?: string; // 平台订单状态
  order_remarks?: string;         // 订单备注
  ioss_number?: string;           // IOSS编号

  // 客户信息
  customer_id: string;            // 客户ID，唯一不可修改
  customer_name: string;          // 客户姓名
  phone1?: string;                // 电话1
  phone2?: string;                // 电话2
  country?: string;               // 国家（中文）
  country_en?: string;            // 国家（英文）
  state_province?: string;        // 所属地区（省/州）
  city?: string;                  // 所属城市
  postal_code?: string;           // 邮政编码
  shipping_address?: string;      // 包裹邮寄地址
  email?: string;                 // 联系邮箱

  // 商品信息
  product_name_en?: string;       // 商品英文名称
  product_sku?: string;           // 商品SKU
  product_quantity?: number;      // 商品SKU数量
  product_unit_price?: number;    // 商品销售单价
  product_total_price?: number;   // 商品销售总价
  shipping_fee?: number;          // 运费收入
  product_image_url?: string;     // 商品图片链接
}

// Platform Types
export interface Platform extends DatabaseRow {
  platform_code: string;        // 平台代码，如amazon、ebay、phh
  platform_name: string;        // 平台显示名称
  platform_name_en: string;     // 平台英文名称
  logo_url?: string;             // 平台Logo图片链接
  status: 'active' | 'inactive'; // 平台状态
  sort_order: number;            // 排序顺序
  config_fields?: any;           // 平台特定配置字段定义
  description?: string;          // 平台描述
}

// Store Types
export interface Store extends DatabaseRow {
  platform_code: string;        // 平台代码，关联platforms表
  store_name: string;            // 店铺名称，可修改
  status: 'active' | 'failed';   // 绑定状态：活跃-正常，失败-绑定失败
  api_key?: string;              // API密钥
  token?: string;                // 短期token，30天有效
  token_expires_at?: Date;       // token过期时间
  site?: string;                 // 平台销售站点
  platform_config?: any;        // 平台特定配置信息（JSON）
}

// 平台特定配置接口
export interface AmazonConfig {
  seller_id: string;
  access_key: string;
  secret_key: string;
  role_arn?: string;
  marketplace?: string;
}

export interface EbayConfig {
  app_id: string;
  dev_id: string;
  cert_id: string;
  user_token: string;
  site?: string;
  store_subscription?: string;
}

export interface PHHConfig {
  email: string;
  password: string;
  shop_id?: string;
}

// Scraping Task Types
export interface ScrapingTask extends DatabaseRow {
  platform: string;
  url: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: any; // JSON object
  error_message?: string;
  completed_at?: Date;
}

// User Types
export interface User extends DatabaseRow {
  username: string;
  email: string;
  password_hash: string;
  role: 'admin' | 'user' | 'viewer';
  last_login?: Date;
  is_active: boolean;
}

// API Response Types
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
  timestamp: string;
}

export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface PaginationResult<T = any> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 扩展的店铺类型（包含关联的平台信息）
export interface StoreWithPlatform extends Store {
  // 关联的平台信息
  platform_name?: string;
  platform_name_en?: string;
  logo_url?: string;
  config_fields?: any;
}

export interface ErrorResponse {
  code: number;
  message: string;
  error: string;
  timestamp: string;
  stack?: string;
  details?: any;
}

// Request Types
export interface PaginationQuery {
  page?: number;
  limit?: number;
}

export interface SearchQuery extends PaginationQuery {
  search?: string;
  sort?: 'asc' | 'desc';
  sortBy?: string;
}

// Category Request Types
export interface CreateCategoryRequest {
  category_code?: string; // 可选，空值会自动生成
  chinese_name: string;
  english_name: string;
  status?: 'enabled' | 'disabled';
  auto_sku?: 'enabled' | 'disabled';
  category_level: 1 | 2 | 3;
  parent_id?: number;
  category_description?: string;
  attribute_tags?: string[];
  sort_order?: number;
}

export interface UpdateCategoryRequest {
  chinese_name?: string;
  english_name?: string;
  status?: 'enabled' | 'disabled';
  auto_sku?: 'enabled' | 'disabled';
  category_description?: string;
  attribute_tags?: string[];
  sort_order?: number;
}

// Product Request Types
export interface CreateProductRequest {
  source: string;
  sku: string;
  ean: string;
  category_id?: number;
  category?: string;
  english_title: string;
  selling_point?: string[];
  english_description: string;
  image1: string;
  image2?: string;
  image3?: string;
  image4?: string;
  image5?: string;
  cost_price?: number;
  package_weight?: number;
  package_length?: number;
  package_width?: number;
  package_height?: number;
  purchase_link?: string;
  remarks?: string;
  status?: 'draft' | 'active' | 'inactive';
}

export interface UpdateProductRequest {
  sku?: string;
  ean?: string;
  category_id?: number;
  category?: string;
  english_title?: string;
  selling_point?: string[];
  english_description?: string;
  image1?: string;
  image2?: string;
  image3?: string;
  image4?: string;
  image5?: string;
  cost_price?: number;
  package_weight?: number;
  package_length?: number;
  package_width?: number;
  package_height?: number;
  purchase_link?: string;
  remarks?: string;
  status?: 'draft' | 'active' | 'inactive';
}

// Authentication Types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  refreshToken: string;
  user: {
    id: number;
    username: string;
    email: string;
    role: string;
  };
}

export interface JWTPayload {
  id: number;
  username: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

// File Upload Types
export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
  buffer: Buffer;
}

export interface UploadResponse {
  url: string;
  filename: string;
  size: number;
  mimetype: string;
}

// Statistics Types
export interface DashboardStats {
  total_products: number;
  active_products: number;
  draft_products: number;
  inactive_products: number;
  total_orders: number;
  pending_orders: number;
  total_revenue: number;
  monthly_revenue: number;
  total_categories: number;
  active_categories: number;
}

// System Setting Types
export interface SystemSetting extends DatabaseRow {
  setting_key: string;
  setting_value: any; // JSON数据
  category: string;
  display_name: string;
  description?: string;
  is_public: boolean;
  is_editable: boolean;
  validation_rules?: any; // JSON数据
  default_value?: any; // JSON数据
  sort_order: number;
}

// Translation Provider Types
export interface TranslationProvider {
  name: string;
  baseUrl?: string;
  timeout: number;
  enabled: boolean;
  globally_enabled: boolean;
  description?: string;
  models?: {
    single_model?: {
      enabled: boolean;
      apiKey: string;
      model: string;
      prompts: {
        title: string;
        description: string;
        selling_point: string;
      };
    };
    multi_model?: {
      enabled: boolean;
      models: Array<{
        name: string;
        apiKey: string;
        model: string;
        prompts: {
          title: string;
          description: string;
          selling_point: string;
        };
      }>;
    };
  };
  providers?: Array<{
    name: string;
    baseUrl: string;
    apiKey: string;
    model: string;
    enabled: boolean;
    prompts: {
      title: string;
      description: string;
      selling_point: string;
    };
  }>;
}

// Translation Scenario Types
export interface TranslationScenario {
  name: string;
  description?: string;
  provider: string;
  fallback_provider?: string;
  enabled: boolean;
  rules?: Record<string, string>;
}

// Translation Config Types
export interface TranslationConfig {
  providers: Record<string, TranslationProvider>;
  scenarios: Record<string, TranslationScenario>;
  defaults: {
    default_source_lang: string;
    default_target_langs: string[];
    auto_translate: boolean;
    translation_timeout: number;
    max_concurrent_requests: number;
    retry_attempts: number;
    retry_delay: number;
  };
}

// System Basic Config Types
export interface SystemBasicConfig {
  company_name: string;
  system_title: string;
  proxy_host: string;
  proxy_port: string;
  proxy_username: string;
  proxy_password: string;
}

// Translation Service Config Types
export interface TranslationServiceConfig {
  provider: string;
  sub_service: string | null;
}

// Translation Scenarios Types
export interface TranslationScenarios {
  [platformCode: string]: {
    platform_name: string;
    enabled: boolean;
    scenarios: {
      form_editing: TranslationScenario;
      batch_translation: TranslationScenario;
      translation_task: TranslationScenario;
    };
  };
}

export interface TranslationScenario {
  name: string;
  description: string;
  enabled: boolean;
  content_types: {
    title: string | TranslationServiceConfig;
    description: string | TranslationServiceConfig;
    selling_point: string | TranslationServiceConfig;
  };
}

// Token Statistics Types
export interface TokenStatistics {
  deepseek_huoshan: {
    models: {
      [modelName: string]: TokenModelStats;
    };
  };
  openai_compatible: {
    models: {
      [modelName: string]: TokenModelStats;
    };
  };
  global_statistics: {
    total_input_tokens: number;
    total_output_tokens: number;
    total_api_calls: number;
  };
}

export interface TokenModelStats {
  yesterday_input_tokens: number;
  yesterday_output_tokens: number;
  today_input_tokens: number;
  today_output_tokens: number;
  total_input_tokens: number;
  total_output_tokens: number;
}

// Export all types
