/**
 * Category Controller
 * Handles product category management operations
 */

import { Request, Response, NextFunction } from 'express';
import { categoryService } from '@/services/products/categoryService';
import { logger } from '@/utils/logger';
import { getBeijingTimeISO } from '@/utils/time';
import {
  CreateCategoryRequest,
  UpdateCategoryRequest,
  PaginationQuery
} from '@/types';

class CategoryController {
  // GET /api/v1/categories
  public async getCategories(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query = req.query as PaginationQuery & {
        level?: number;
        parent_id?: number;
        status?: 'enabled' | 'disabled';
      };

      const result = await categoryService.getCategories(query);

      res.status(200).json({
        code: 200,
        message: 'Categories retrieved successfully',
        data: result,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/v1/categories/getbytree
  public async getCategoryTree(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const tree = await categoryService.getCategoryTree();

      res.status(200).json({
        code: 200,
        message: 'Category tree retrieved successfully',
        data: tree,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/v1/categories/level/:level
  public async getCategoriesByLevel(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const level = parseInt(req.params.level!) as 1 | 2 | 3 | 4 | 5 | 6 | 7;
      const categories = await categoryService.getCategoriesByLevel(level);

      res.status(200).json({
        code: 200,
        message: `Level ${level} categories retrieved successfully`,
        data: categories,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // POST /api/v1/categories
  public async createCategory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const categoryData: CreateCategoryRequest = req.body;
      const category = await categoryService.createCategory(categoryData);

      logger.info('Category created:', { categoryId: category.id, code: category.category_code });

      res.status(201).json({
        code: 201,
        message: 'Category created successfully',
        data: category,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // POST /api/v1/categories/batch
  public async batchCreateCategories(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { categories }: { categories: CreateCategoryRequest[] } = req.body;
      const result = await categoryService.batchCreateCategories(categories);

      logger.info('Batch categories created:', { count: result.length });

      res.status(201).json({
        code: 201,
        message: 'Categories created successfully',
        data: result,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/v1/categories/:id
  public async getCategoryById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      const category = await categoryService.getCategoryById(id);

      res.status(200).json({
        code: 200,
        message: 'Category retrieved successfully',
        data: category,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // PUT /api/v1/categories/:id
  public async updateCategory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      const updateData: UpdateCategoryRequest = req.body;
      const category = await categoryService.updateCategory(id, updateData);

      logger.info('Category updated:', { categoryId: id });

      res.status(200).json({
        code: 200,
        message: 'Category updated successfully',
        data: category,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // PUT /api/v1/categories/:id/status
  public async updateCategoryStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      const { status }: { status: 'enabled' | 'disabled' } = req.body;
      const category = await categoryService.updateCategoryStatus(id, status);

      logger.info('Category status updated:', { categoryId: id, status });

      res.status(200).json({
        code: 200,
        message: 'Category status updated successfully',
        data: category,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // DELETE /api/v1/categories/:id
  public async deleteCategory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      await categoryService.deleteCategory(id);

      logger.info('Category deleted:', { categoryId: id });

      res.status(200).json({
        code: 200,
        message: 'Category deleted successfully',
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/v1/categories/:id/children
  public async getCategoryChildren(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      const children = await categoryService.getCategoryChildren(id);

      res.status(200).json({
        code: 200,
        message: 'Category children retrieved successfully',
        data: children,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }
}

export const categoryController = new CategoryController();
