"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/catalog/page",{

/***/ "(app-pages-browser)/./src/components/products/product-catalog-page.tsx":
/*!**********************************************************!*\
  !*** ./src/components/products/product-catalog-page.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductCatalogPage: function() { return /* binding */ ProductCatalogPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/expand.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/hooks/useCategories */ \"(app-pages-browser)/./src/hooks/useCategories.ts\");\n/* __next_internal_client_entry_do_not_use__ ProductCatalogPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductCatalogPage() {\n    _s();\n    const [expandedNodes, setExpandedNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"1\",\n        \"2\"\n    ]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedNodeLevel, setSelectedNodeLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 存储选中节点的层级\n    ;\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdding, setIsAdding] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_11__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    // 使用API hooks\n    const { categoryTree: apiCategoryTree, loading: categoriesLoading, error: categoriesError, refetch: refetchCategories } = (0,_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategoryTree)();\n    const { createCategory, updateCategory, deleteCategory, loading: operationLoading } = (0,_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategories)();\n    // 转换API数据为显示格式\n    const convertCategoryToNode = (category)=>({\n            id: category.id.toString(),\n            name: category.chinese_name,\n            englishName: category.english_name,\n            categoryCode: category.category_code,\n            description: category.category_description || \"\",\n            tags: category.attribute_tags || [],\n            autoSku: category.auto_sku === \"enabled\",\n            createdAt: new Date(category.created_at).toLocaleString(\"zh-CN\"),\n            status: category.status === \"enabled\" ? \"active\" : \"inactive\",\n            children: category.children ? category.children.map(convertCategoryToNode) : undefined\n        });\n    const catalogTree = apiCategoryTree ? apiCategoryTree.map(convertCategoryToNode) : [];\n    const toggleExpanded = (nodeId)=>{\n        setExpandedNodes((prev)=>prev.includes(nodeId) ? prev.filter((id)=>id !== nodeId) : [\n                ...prev,\n                nodeId\n            ]);\n    };\n    // 递归获取所有有子节点的节点ID\n    const getAllExpandableNodeIds = (nodes)=>{\n        const ids = [];\n        const traverse = (nodeList)=>{\n            nodeList.forEach((node)=>{\n                if (node.children && node.children.length > 0) {\n                    ids.push(node.id);\n                    traverse(node.children);\n                }\n            });\n        };\n        traverse(nodes);\n        return ids;\n    };\n    // 展开全部\n    const handleExpandAll = ()=>{\n        const allExpandableIds = getAllExpandableNodeIds(catalogTree);\n        setExpandedNodes(allExpandableIds);\n    };\n    // 折叠全部\n    const handleCollapseAll = ()=>{\n        setExpandedNodes([]);\n    };\n    // 递归搜索匹配的节点\n    const searchNodes = (nodes, searchTerm)=>{\n        if (!searchTerm.trim()) return nodes;\n        const results = [];\n        const lowerSearchTerm = searchTerm.toLowerCase();\n        const searchInNode = (node)=>{\n            var _node_categoryCode;\n            // 检查当前节点是否匹配\n            const nameMatch = node.name.toLowerCase().includes(lowerSearchTerm);\n            const englishNameMatch = node.englishName.toLowerCase().includes(lowerSearchTerm);\n            const codeMatch = (_node_categoryCode = node.categoryCode) === null || _node_categoryCode === void 0 ? void 0 : _node_categoryCode.toLowerCase().includes(lowerSearchTerm);\n            const isCurrentMatch = nameMatch || englishNameMatch || codeMatch;\n            // 递归搜索子节点\n            const matchingChildren = [];\n            if (node.children) {\n                node.children.forEach((child)=>{\n                    const childResult = searchInNode(child);\n                    if (childResult) {\n                        matchingChildren.push(childResult);\n                    }\n                });\n            }\n            // 如果当前节点匹配或有匹配的子节点，则返回节点\n            if (isCurrentMatch || matchingChildren.length > 0) {\n                return {\n                    ...node,\n                    children: matchingChildren.length > 0 ? matchingChildren : node.children\n                };\n            }\n            return null;\n        };\n        nodes.forEach((node)=>{\n            const result = searchInNode(node);\n            if (result) {\n                results.push(result);\n            }\n        });\n        return results;\n    };\n    // 获取搜索结果中所有匹配节点的路径（用于自动展开）\n    const getMatchingNodePaths = (nodes, searchTerm)=>{\n        if (!searchTerm.trim()) return [];\n        const paths = [];\n        const lowerSearchTerm = searchTerm.toLowerCase();\n        const findPaths = function(nodeList) {\n            let currentPath = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n            nodeList.forEach((node)=>{\n                var _node_categoryCode;\n                const newPath = [\n                    ...currentPath,\n                    node.id\n                ];\n                // 检查当前节点是否匹配\n                const nameMatch = node.name.toLowerCase().includes(lowerSearchTerm);\n                const englishNameMatch = node.englishName.toLowerCase().includes(lowerSearchTerm);\n                const codeMatch = (_node_categoryCode = node.categoryCode) === null || _node_categoryCode === void 0 ? void 0 : _node_categoryCode.toLowerCase().includes(lowerSearchTerm);\n                if (nameMatch || englishNameMatch || codeMatch) {\n                    // 添加到匹配节点的所有父节点路径\n                    newPath.slice(0, -1).forEach((parentId)=>{\n                        if (!paths.includes(parentId)) {\n                            paths.push(parentId);\n                        }\n                    });\n                }\n                // 递归搜索子节点\n                if (node.children && node.children.length > 0) {\n                    findPaths(node.children, newPath);\n                }\n            });\n        };\n        findPaths(nodes);\n        return paths;\n    };\n    // 处理搜索\n    const handleSearch = ()=>{\n        if (!searchValue.trim()) {\n            // 如果搜索框为空，重置展开状态\n            setExpandedNodes([\n                \"1\",\n                \"2\"\n            ]) // 恢复默认展开状态\n            ;\n            return;\n        }\n        // 获取匹配节点的父节点路径并自动展开\n        const pathsToExpand = getMatchingNodePaths(catalogTree, searchValue);\n        setExpandedNodes(pathsToExpand);\n    };\n    // 重置搜索\n    const handleResetSearch = ()=>{\n        setSearchValue(\"\");\n    };\n    // 过滤后的目录树（用于显示搜索结果）\n    const filteredCatalogTree = searchValue.trim() ? searchNodes(catalogTree, searchValue) : catalogTree;\n    const handleAdd = ()=>{\n        setIsAdding(true);\n        setIsEditing(false);\n        setSelectedNode(null);\n        setSelectedNodeLevel(0) // 一级分类的层级为0\n        ;\n        setShowEditDialog(true);\n        setFormData({\n            name: \"\",\n            englishName: \"\",\n            categoryCode: \"\",\n            description: \"\",\n            tags: [],\n            autoSku: false,\n            status: \"active\"\n        });\n    };\n    const handleEditNode = (node)=>{\n        setSelectedNode(node);\n        setIsEditing(true);\n        setIsAdding(false);\n        setShowEditDialog(true);\n        setFormData({\n            name: node.name,\n            englishName: node.englishName,\n            categoryCode: node.categoryCode,\n            description: node.description,\n            tags: node.tags,\n            autoSku: node.autoSku,\n            status: node.status\n        });\n    };\n    const handleDeleteNode = async (node)=>{\n        const confirmed = await confirm({\n            title: \"删除分类\",\n            description: '确定要删除分类\"'.concat(node.name, '\"吗？此操作不可撤销。'),\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (confirmed) {\n            try {\n                await deleteCategory(parseInt(node.id));\n                refetchCategories();\n                // 显示删除成功提示\n                toast({\n                    title: \"删除成功\"\n                });\n            } catch (error) {\n                console.error(\"删除失败:\", error);\n                // 显示删除失败提示\n                toast({\n                    title: \"删除失败\",\n                    description: error instanceof Error ? error.message : \"删除时发生未知错误\",\n                    variant: \"destructive\"\n                });\n            }\n        }\n    };\n    const handleAddChild = (parentNode, parentLevel)=>{\n        setIsAdding(true);\n        setIsEditing(false);\n        setSelectedNode(parentNode);\n        setSelectedNodeLevel(parentLevel) // 存储父节点的层级\n        ;\n        setShowEditDialog(true);\n        setFormData({\n            name: \"\",\n            englishName: \"\",\n            categoryCode: \"\",\n            description: \"\",\n            tags: [],\n            autoSku: false,\n            status: \"active\"\n        });\n    };\n    const handleSave = async ()=>{\n        try {\n            var _formData_name, _formData_englishName;\n            // 验证必填字段\n            if (!((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim())) {\n                toast({\n                    title: \"验证失败\",\n                    description: \"请输入中文名称\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (!((_formData_englishName = formData.englishName) === null || _formData_englishName === void 0 ? void 0 : _formData_englishName.trim())) {\n                toast({\n                    title: \"验证失败\",\n                    description: \"请输入英文名称\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (isAdding) {\n                var _formData_categoryCode, _formData_description;\n                // 创建新分类\n                const categoryCode = (_formData_categoryCode = formData.categoryCode) === null || _formData_categoryCode === void 0 ? void 0 : _formData_categoryCode.trim();\n                // 根据是否有选中的父节点来确定层级和父ID\n                let categoryLevel = 1;\n                let parentId = null;\n                if (selectedNode) {\n                    // 如果有选中的父节点，说明是添加子分类\n                    parentId = parseInt(selectedNode.id);\n                    // 根据父节点的层级确定子节点层级\n                    // selectedNodeLevel: 0=一级, 1=二级, 2=三级, 3=四级, 4=五级, 5=六级 (前端显示层级)\n                    // category_level: 1=一级, 2=二级, 3=三级, 4=四级, 5=五级, 6=六级, 7=七级 (后端数据库层级)\n                    // 子分类的层级 = 父分类的数据库层级 + 1 = (selectedNodeLevel + 1) + 1\n                    categoryLevel = selectedNodeLevel + 2;\n                }\n                const createData = {\n                    // 分类编码处理：如果有值则转换为大写，如果为空则不传递该字段\n                    ...categoryCode ? {\n                        category_code: categoryCode.toUpperCase()\n                    } : {},\n                    chinese_name: formData.name.trim(),\n                    english_name: formData.englishName.trim(),\n                    status: formData.status === \"active\" ? \"enabled\" : \"disabled\",\n                    auto_sku: formData.autoSku ? \"enabled\" : \"disabled\",\n                    category_level: categoryLevel,\n                    parent_id: parentId,\n                    category_description: ((_formData_description = formData.description) === null || _formData_description === void 0 ? void 0 : _formData_description.trim()) || \"\",\n                    attribute_tags: formData.tags || [],\n                    sort_order: 0\n                };\n                await createCategory(createData);\n                setIsAdding(false);\n                setShowEditDialog(false);\n                setFormData({}) // 清空表单数据\n                ;\n                await refetchCategories() // 等待刷新完成\n                ;\n                // 显示成功提示\n                toast({\n                    title: \"创建成功\"\n                });\n            } else if (selectedNode && isEditing) {\n                var _formData_description1;\n                // 更新分类 - 编辑时不允许修改分类编码\n                const updateData = {\n                    chinese_name: formData.name.trim(),\n                    english_name: formData.englishName.trim(),\n                    status: formData.status === \"active\" ? \"enabled\" : \"disabled\",\n                    auto_sku: formData.autoSku ? \"enabled\" : \"disabled\",\n                    category_description: ((_formData_description1 = formData.description) === null || _formData_description1 === void 0 ? void 0 : _formData_description1.trim()) || \"\",\n                    attribute_tags: formData.tags || []\n                };\n                await updateCategory(parseInt(selectedNode.id), updateData);\n                setIsEditing(false);\n                setShowEditDialog(false);\n                await refetchCategories() // 等待刷新完成\n                ;\n                // 显示成功提示\n                toast({\n                    title: \"更新成功\"\n                });\n            }\n        } catch (error) {\n            console.error(\"保存失败:\", error);\n            // 显示错误提示\n            toast({\n                title: \"保存失败\",\n                description: error instanceof Error ? error.message : \"保存时发生未知错误\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleCancel = ()=>{\n        setIsEditing(false);\n        setIsAdding(false);\n        setShowEditDialog(false);\n        setFormData({});\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            placeholder: \"搜索目录\",\n                                            value: searchValue,\n                                            onChange: (e)=>{\n                                                setSearchValue(e.target.value);\n                                                // 实时搜索：输入时自动触发搜索\n                                                if (e.target.value.trim()) {\n                                                    const pathsToExpand = getMatchingNodePaths(catalogTree, e.target.value);\n                                                    setExpandedNodes(pathsToExpand);\n                                                } else {\n                                                    setExpandedNodes([\n                                                        \"1\",\n                                                        \"2\"\n                                                    ]) // 恢复默认展开状态\n                                                    ;\n                                                }\n                                            },\n                                            onKeyDown: (e)=>{\n                                                if (e.key === \"Enter\") {\n                                                    handleSearch();\n                                                }\n                                            },\n                                            className: \"w-80\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: handleResetSearch,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"重置\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleExpandAll,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"展开全部\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleCollapseAll,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"折叠全部\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        onClick: handleAdd,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"新增一级产品目录\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0\",\n                    children: categoriesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-muted-foreground\",\n                                children: \"加载中...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 13\n                    }, this) : categoriesError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm mb-2\",\n                                children: [\n                                    \"加载失败: \",\n                                    categoriesError\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: refetchCategories,\n                                children: \"重试\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm border-separate border-spacing-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"bg-muted/30 border-b\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"名称 / 英文名\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-32 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"描述\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-20 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"SKU数量\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"分类编码\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"报关编码\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"属性栏目\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-32 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"更新时间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground\",\n                                                children: \"操作\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: filteredCatalogTree.length > 0 ? filteredCatalogTree.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableTreeNode, {\n                                            node: node,\n                                            level: 0,\n                                            expandedNodes: expandedNodes,\n                                            onToggleExpanded: toggleExpanded,\n                                            onEdit: handleEditNode,\n                                            onDelete: handleDeleteNode,\n                                            onAddChild: handleAddChild,\n                                            searchTerm: searchValue\n                                        }, node.id, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 23\n                                        }, this)) : searchValue.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: 8,\n                                            className: \"p-8 text-center text-muted-foreground\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-8 h-8 text-muted-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"未找到匹配的目录\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: \"请尝试其他关键词或检查拼写\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: 8,\n                                            className: \"p-8 text-center text-muted-foreground\",\n                                            children: \"暂无数据\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 473,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 472,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showEditDialog,\n                onOpenChange: setShowEditDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"max-w-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                children: isAdding ? selectedNode ? \"添加\".concat(selectedNodeLevel === 0 ? \"二\" : \"三\", \"级产品目录\") : \"添加一级产品目录\" : \"编辑产品目录\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CatalogForm, {\n                            formData: formData,\n                            setFormData: setFormData,\n                            isEditing: true,\n                            isEditingExisting: !isAdding,\n                            onSave: handleSave,\n                            onCancel: handleCancel,\n                            loading: operationLoading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 542,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 541,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n        lineNumber: 420,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCatalogPage, \"90os8Ia1Am+c2vsT027XTEd1NRM=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_11__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast,\n        _hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategoryTree,\n        _hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategories\n    ];\n});\n_c = ProductCatalogPage;\n// 表格树节点组件\nfunction TableTreeNode(param) {\n    let { node, level, expandedNodes, onToggleExpanded, onEdit, onDelete, onAddChild, searchTerm = \"\" } = param;\n    var _node_children;\n    const hasChildren = node.children && node.children.length > 0;\n    const isExpanded = expandedNodes.includes(node.id);\n    // 高亮显示匹配的文本\n    const highlightText = (text, searchTerm)=>{\n        if (!searchTerm.trim()) return text;\n        const regex = new RegExp(\"(\".concat(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \")\"), \"gi\");\n        const parts = text.split(regex);\n        return parts.map((part, index)=>regex.test(part) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n                className: \"bg-yellow-200 text-yellow-900 px-1 rounded\",\n                children: part\n            }, index, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 601,\n                columnNumber: 9\n            }, this) : part);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            style: {\n                                paddingLeft: \"\".concat(level * 20, \"px\")\n                            },\n                            children: [\n                                hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>onToggleExpanded(node.id),\n                                    className: \"p-0.5 hover:bg-muted rounded\",\n                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-foreground\",\n                                            children: highlightText(node.name, searchTerm)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: highlightText(node.englishName, searchTerm)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 612,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 611,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground truncate max-w-32\",\n                            title: node.description,\n                            children: node.description || \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 643,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: \"outline\",\n                            className: \"text-xs\",\n                            children: node.categoryCode ? highlightText(node.categoryCode, searchTerm) : \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-1\",\n                            children: node.tags.length > 0 ? node.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"text-xs\",\n                                    children: tag\n                                }, index, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 17\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"--\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 654,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 653,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: node.createdAt\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 666,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onEdit(node),\n                                    className: \"h-7 px-2 text-xs text-blue-600 hover:text-blue-800\",\n                                    children: \"编辑\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 13\n                                }, this),\n                                level < 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onAddChild(node, level),\n                                    className: \"h-7 px-2 text-xs text-green-600 hover:text-green-800\",\n                                    children: [\n                                        \"新增\",\n                                        [\n                                            \"二\",\n                                            \"三\",\n                                            \"四\",\n                                            \"五\",\n                                            \"六\",\n                                            \"七\"\n                                        ][level],\n                                        \"级目录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onDelete(node),\n                                    className: \"h-7 px-2 text-xs text-red-600 hover:text-red-800\",\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 691,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 671,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 610,\n                columnNumber: 7\n            }, this),\n            hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: (_node_children = node.children) === null || _node_children === void 0 ? void 0 : _node_children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableTreeNode, {\n                        node: child,\n                        level: level + 1,\n                        expandedNodes: expandedNodes,\n                        onToggleExpanded: onToggleExpanded,\n                        onEdit: onEdit,\n                        onDelete: onDelete,\n                        onAddChild: onAddChild,\n                        searchTerm: searchTerm\n                    }, child.id, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 706,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false)\n        ]\n    }, void 0, true);\n}\n_c1 = TableTreeNode;\n// 分类表单组件\nfunction CatalogForm(param) {\n    let { formData, setFormData, isEditing, isEditingExisting = false, onSave, onCancel, loading } = param;\n    const availableTags = [\n        \"color\",\n        \"size\",\n        \"model\",\n        \"material\",\n        \"style\"\n    ];\n    const updateFormData = (field, value)=>{\n        setFormData({\n            ...formData,\n            [field]: value\n        });\n    };\n    if (isEditing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"name\",\n                                    children: [\n                                        \"中文名称 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 40\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"name\",\n                                    value: formData.name || \"\",\n                                    onChange: (e)=>updateFormData(\"name\", e.target.value),\n                                    placeholder: \"请输入中文名称\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 752,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"englishName\",\n                                    children: [\n                                        \"英文名称 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 47\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 763,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"englishName\",\n                                    value: formData.englishName || \"\",\n                                    onChange: (e)=>updateFormData(\"englishName\", e.target.value),\n                                    placeholder: \"请输入英文名称\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 764,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 762,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 751,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"categoryCode\",\n                                    children: [\n                                        \"分类编码 \",\n                                        isEditingExisting ? \"(不可修改)\" : \"(可选)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 776,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"categoryCode\",\n                                    value: formData.categoryCode || \"\",\n                                    onChange: (e)=>{\n                                        if (!isEditingExisting) {\n                                            // 只有在创建新分类时才允许修改\n                                            const value = e.target.value.toUpperCase().replace(/[^A-Z0-9_-]/g, \"\");\n                                            updateFormData(\"categoryCode\", value);\n                                        }\n                                    },\n                                    placeholder: isEditingExisting ? \"分类编码创建后不可修改\" : \"如：CLOTHING\",\n                                    disabled: isEditingExisting,\n                                    className: isEditingExisting ? \"bg-muted cursor-not-allowed\" : \"\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 779,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: isEditingExisting ? \"分类编码在创建后不能修改，以确保数据一致性\" : \"用于自动生成SKU，支持字母数字下划线连字符，会自动转换为大写\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 793,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 775,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"status\",\n                                    children: \"状态\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 801,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                    value: formData.status || \"active\",\n                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                placeholder: \"选择状态\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"active\",\n                                                    children: \"启用\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"inactive\",\n                                                    children: \"禁用\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 802,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 800,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 774,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                            htmlFor: \"description\",\n                            children: \"描述\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 818,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                            id: \"description\",\n                            value: formData.description || \"\",\n                            onChange: (e)=>updateFormData(\"description\", e.target.value),\n                            placeholder: \"请输入目录描述\",\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 819,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 817,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                            htmlFor: \"tags\",\n                            children: \"属性标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 829,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                            id: \"tags\",\n                            value: Array.isArray(formData.tags) ? formData.tags.join(\", \") : \"\",\n                            onChange: (e)=>updateFormData(\"tags\", e.target.value.split(\",\").map((t)=>t.trim()).filter((t)=>t)),\n                            placeholder: \"color, size\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 830,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: [\n                                \"可选标签: \",\n                                availableTags.join(\", \")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 836,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 828,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                            id: \"autoSku\",\n                            checked: !!formData.autoSku,\n                            onCheckedChange: (checked)=>updateFormData(\"autoSku\", !!checked)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 842,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                            htmlFor: \"autoSku\",\n                            children: \"自动生成SKU\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 847,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 841,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-end gap-2 pt-4 border-t\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            onClick: onCancel,\n                            disabled: loading,\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 852,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            onClick: onSave,\n                            disabled: loading,\n                            children: loading ? \"保存中...\" : \"确定\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 860,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 851,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n            lineNumber: 750,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n_c2 = CatalogForm;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ProductCatalogPage\");\n$RefreshReg$(_c1, \"TableTreeNode\");\n$RefreshReg$(_c2, \"CatalogForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/products/product-catalog-page.tsx\n"));

/***/ })

});