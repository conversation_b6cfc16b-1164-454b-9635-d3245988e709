globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers.tsx":{"*":{"id":"(ssr)/./src/components/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/auth/auth-guard.tsx":{"*":{"id":"(ssr)/./src/components/auth/auth-guard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/dashboard-layout.tsx":{"*":{"id":"(ssr)/./src/components/layout/dashboard-layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/dashboard-stats.tsx":{"*":{"id":"(ssr)/./src/components/dashboard/dashboard-stats.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/product-overview.tsx":{"*":{"id":"(ssr)/./src/components/dashboard/product-overview.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/quick-actions.tsx":{"*":{"id":"(ssr)/./src/components/dashboard/quick-actions.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/recent-tasks.tsx":{"*":{"id":"(ssr)/./src/components/dashboard/recent-tasks.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\src\\components\\providers.tsx":{"id":"(app-pages-browser)/./src/components/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\src\\components\\auth\\auth-guard.tsx":{"id":"(app-pages-browser)/./src/components/auth/auth-guard.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\src\\components\\layout\\dashboard-layout.tsx":{"id":"(app-pages-browser)/./src/components/layout/dashboard-layout.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\src\\components\\dashboard\\dashboard-stats.tsx":{"id":"(app-pages-browser)/./src/components/dashboard/dashboard-stats.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\src\\components\\dashboard\\product-overview.tsx":{"id":"(app-pages-browser)/./src/components/dashboard/product-overview.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\src\\components\\dashboard\\quick-actions.tsx":{"id":"(app-pages-browser)/./src/components/dashboard/quick-actions.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\src\\components\\dashboard\\recent-tasks.tsx":{"id":"(app-pages-browser)/./src/components/dashboard/recent-tasks.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false}},"entryCSSFiles":{"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\src\\app\\layout":["static/css/app/layout.css"],"D:\\在云端工作记录\\Github\\ikun_erp\\ikun\\frontend\\src\\app\\page":[]}}