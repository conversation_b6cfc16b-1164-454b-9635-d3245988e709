"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/catalog/page",{

/***/ "(app-pages-browser)/./src/components/products/product-catalog-page.tsx":
/*!**********************************************************!*\
  !*** ./src/components/products/product-catalog-page.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductCatalogPage: function() { return /* binding */ ProductCatalogPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/expand.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/hooks/useCategories */ \"(app-pages-browser)/./src/hooks/useCategories.ts\");\n/* __next_internal_client_entry_do_not_use__ ProductCatalogPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductCatalogPage() {\n    _s();\n    const [expandedNodes, setExpandedNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"1\",\n        \"2\"\n    ]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedNodeLevel, setSelectedNodeLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 存储选中节点的层级\n    ;\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdding, setIsAdding] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_11__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    // 使用API hooks\n    const { categoryTree: apiCategoryTree, loading: categoriesLoading, error: categoriesError, refetch: refetchCategories } = (0,_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategoryTree)();\n    const { createCategory, updateCategory, deleteCategory, loading: operationLoading } = (0,_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategories)();\n    // 转换API数据为显示格式\n    const convertCategoryToNode = (category)=>({\n            id: category.id.toString(),\n            name: category.chinese_name,\n            englishName: category.english_name,\n            categoryCode: category.category_code || \"\",\n            description: category.category_description || \"\",\n            tags: category.attribute_tags || [],\n            autoSku: category.auto_sku === \"enabled\",\n            createdAt: new Date(category.created_at).toLocaleString(\"zh-CN\"),\n            status: category.status === \"enabled\" ? \"active\" : \"inactive\",\n            children: category.children ? category.children.map(convertCategoryToNode) : undefined\n        });\n    const catalogTree = apiCategoryTree ? apiCategoryTree.map(convertCategoryToNode) : [];\n    const toggleExpanded = (nodeId)=>{\n        setExpandedNodes((prev)=>prev.includes(nodeId) ? prev.filter((id)=>id !== nodeId) : [\n                ...prev,\n                nodeId\n            ]);\n    };\n    // 递归获取所有有子节点的节点ID\n    const getAllExpandableNodeIds = (nodes)=>{\n        const ids = [];\n        const traverse = (nodeList)=>{\n            nodeList.forEach((node)=>{\n                if (node.children && node.children.length > 0) {\n                    ids.push(node.id);\n                    traverse(node.children);\n                }\n            });\n        };\n        traverse(nodes);\n        return ids;\n    };\n    // 展开全部\n    const handleExpandAll = ()=>{\n        const allExpandableIds = getAllExpandableNodeIds(catalogTree);\n        setExpandedNodes(allExpandableIds);\n    };\n    // 折叠全部\n    const handleCollapseAll = ()=>{\n        setExpandedNodes([]);\n    };\n    // 递归搜索匹配的节点\n    const searchNodes = (nodes, searchTerm)=>{\n        if (!searchTerm.trim()) return nodes;\n        const results = [];\n        const lowerSearchTerm = searchTerm.toLowerCase();\n        const searchInNode = (node)=>{\n            var _node_categoryCode;\n            // 检查当前节点是否匹配\n            const nameMatch = node.name.toLowerCase().includes(lowerSearchTerm);\n            const englishNameMatch = node.englishName.toLowerCase().includes(lowerSearchTerm);\n            const codeMatch = (_node_categoryCode = node.categoryCode) === null || _node_categoryCode === void 0 ? void 0 : _node_categoryCode.toLowerCase().includes(lowerSearchTerm);\n            const isCurrentMatch = nameMatch || englishNameMatch || codeMatch;\n            // 递归搜索子节点\n            const matchingChildren = [];\n            if (node.children) {\n                node.children.forEach((child)=>{\n                    const childResult = searchInNode(child);\n                    if (childResult) {\n                        matchingChildren.push(childResult);\n                    }\n                });\n            }\n            // 如果当前节点匹配或有匹配的子节点，则返回节点\n            if (isCurrentMatch || matchingChildren.length > 0) {\n                return {\n                    ...node,\n                    children: matchingChildren.length > 0 ? matchingChildren : node.children\n                };\n            }\n            return null;\n        };\n        nodes.forEach((node)=>{\n            const result = searchInNode(node);\n            if (result) {\n                results.push(result);\n            }\n        });\n        return results;\n    };\n    // 获取搜索结果中所有匹配节点的路径（用于自动展开）\n    const getMatchingNodePaths = (nodes, searchTerm)=>{\n        if (!searchTerm.trim()) return [];\n        const paths = [];\n        const lowerSearchTerm = searchTerm.toLowerCase();\n        const findPaths = function(nodeList) {\n            let currentPath = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n            nodeList.forEach((node)=>{\n                var _node_categoryCode;\n                const newPath = [\n                    ...currentPath,\n                    node.id\n                ];\n                // 检查当前节点是否匹配\n                const nameMatch = node.name.toLowerCase().includes(lowerSearchTerm);\n                const englishNameMatch = node.englishName.toLowerCase().includes(lowerSearchTerm);\n                const codeMatch = (_node_categoryCode = node.categoryCode) === null || _node_categoryCode === void 0 ? void 0 : _node_categoryCode.toLowerCase().includes(lowerSearchTerm);\n                if (nameMatch || englishNameMatch || codeMatch) {\n                    // 添加到匹配节点的所有父节点路径\n                    newPath.slice(0, -1).forEach((parentId)=>{\n                        if (!paths.includes(parentId)) {\n                            paths.push(parentId);\n                        }\n                    });\n                }\n                // 递归搜索子节点\n                if (node.children && node.children.length > 0) {\n                    findPaths(node.children, newPath);\n                }\n            });\n        };\n        findPaths(nodes);\n        return paths;\n    };\n    // 处理搜索\n    const handleSearch = ()=>{\n        if (!searchValue.trim()) {\n            // 如果搜索框为空，重置展开状态\n            setExpandedNodes([\n                \"1\",\n                \"2\"\n            ]) // 恢复默认展开状态\n            ;\n            return;\n        }\n        // 获取匹配节点的父节点路径并自动展开\n        const pathsToExpand = getMatchingNodePaths(catalogTree, searchValue);\n        setExpandedNodes(pathsToExpand);\n    };\n    // 重置搜索\n    const handleResetSearch = ()=>{\n        setSearchValue(\"\");\n    };\n    // 过滤后的目录树（用于显示搜索结果）\n    const filteredCatalogTree = searchValue.trim() ? searchNodes(catalogTree, searchValue) : catalogTree;\n    const handleAdd = ()=>{\n        setIsAdding(true);\n        setIsEditing(false);\n        setSelectedNode(null);\n        setSelectedNodeLevel(0) // 一级分类的层级为0\n        ;\n        setShowEditDialog(true);\n        setFormData({\n            name: \"\",\n            englishName: \"\",\n            categoryCode: \"\",\n            description: \"\",\n            tags: [],\n            autoSku: false,\n            status: \"active\"\n        });\n    };\n    const handleEditNode = (node)=>{\n        setSelectedNode(node);\n        setIsEditing(true);\n        setIsAdding(false);\n        setShowEditDialog(true);\n        setFormData({\n            name: node.name,\n            englishName: node.englishName,\n            categoryCode: node.categoryCode,\n            description: node.description,\n            tags: node.tags,\n            autoSku: node.autoSku,\n            status: node.status\n        });\n    };\n    const handleDeleteNode = async (node)=>{\n        const confirmed = await confirm({\n            title: \"删除分类\",\n            description: '确定要删除分类\"'.concat(node.name, '\"吗？此操作不可撤销。'),\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (confirmed) {\n            try {\n                await deleteCategory(parseInt(node.id));\n                refetchCategories();\n                // 显示删除成功提示\n                toast({\n                    title: \"删除成功\"\n                });\n            } catch (error) {\n                console.error(\"删除失败:\", error);\n                // 显示删除失败提示\n                toast({\n                    title: \"删除失败\",\n                    description: error instanceof Error ? error.message : \"删除时发生未知错误\",\n                    variant: \"destructive\"\n                });\n            }\n        }\n    };\n    const handleAddChild = (parentNode, parentLevel)=>{\n        setIsAdding(true);\n        setIsEditing(false);\n        setSelectedNode(parentNode);\n        setSelectedNodeLevel(parentLevel) // 存储父节点的层级\n        ;\n        setShowEditDialog(true);\n        setFormData({\n            name: \"\",\n            englishName: \"\",\n            categoryCode: \"\",\n            description: \"\",\n            tags: [],\n            autoSku: false,\n            status: \"active\"\n        });\n    };\n    const handleSave = async ()=>{\n        try {\n            var _formData_name, _formData_englishName;\n            // 验证必填字段\n            if (!((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim())) {\n                toast({\n                    title: \"验证失败\",\n                    description: \"请输入中文名称\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (!((_formData_englishName = formData.englishName) === null || _formData_englishName === void 0 ? void 0 : _formData_englishName.trim())) {\n                toast({\n                    title: \"验证失败\",\n                    description: \"请输入英文名称\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (isAdding) {\n                var _formData_categoryCode, _formData_description;\n                // 创建新分类\n                const categoryCode = (_formData_categoryCode = formData.categoryCode) === null || _formData_categoryCode === void 0 ? void 0 : _formData_categoryCode.trim();\n                // 根据是否有选中的父节点来确定层级和父ID\n                let categoryLevel = 1;\n                let parentId = null;\n                if (selectedNode) {\n                    // 如果有选中的父节点，说明是添加子分类\n                    parentId = parseInt(selectedNode.id);\n                    // 根据父节点的层级确定子节点层级\n                    // selectedNodeLevel: 0=一级, 1=二级, 2=三级, 3=四级, 4=五级, 5=六级 (前端显示层级)\n                    // category_level: 1=一级, 2=二级, 3=三级, 4=四级, 5=五级, 6=六级, 7=七级 (后端数据库层级)\n                    // 子分类的层级 = 父分类的数据库层级 + 1 = (selectedNodeLevel + 1) + 1\n                    categoryLevel = selectedNodeLevel + 2;\n                }\n                const createData = {\n                    // 分类编码处理：如果有值则转换为大写，如果为空则不传递该字段\n                    ...categoryCode ? {\n                        category_code: categoryCode.toUpperCase()\n                    } : {},\n                    chinese_name: formData.name.trim(),\n                    english_name: formData.englishName.trim(),\n                    status: formData.status === \"active\" ? \"enabled\" : \"disabled\",\n                    auto_sku: formData.autoSku ? \"enabled\" : \"disabled\",\n                    category_level: categoryLevel,\n                    parent_id: parentId,\n                    category_description: ((_formData_description = formData.description) === null || _formData_description === void 0 ? void 0 : _formData_description.trim()) || \"\",\n                    attribute_tags: formData.tags || [],\n                    sort_order: 0\n                };\n                await createCategory(createData);\n                setIsAdding(false);\n                setShowEditDialog(false);\n                setFormData({}) // 清空表单数据\n                ;\n                await refetchCategories() // 等待刷新完成\n                ;\n                // 显示成功提示\n                toast({\n                    title: \"创建成功\"\n                });\n            } else if (selectedNode && isEditing) {\n                var _formData_description1;\n                // 更新分类 - 编辑时不允许修改分类编码\n                const updateData = {\n                    chinese_name: formData.name.trim(),\n                    english_name: formData.englishName.trim(),\n                    status: formData.status === \"active\" ? \"enabled\" : \"disabled\",\n                    auto_sku: formData.autoSku ? \"enabled\" : \"disabled\",\n                    category_description: ((_formData_description1 = formData.description) === null || _formData_description1 === void 0 ? void 0 : _formData_description1.trim()) || \"\",\n                    attribute_tags: formData.tags || []\n                };\n                await updateCategory(parseInt(selectedNode.id), updateData);\n                setIsEditing(false);\n                setShowEditDialog(false);\n                await refetchCategories() // 等待刷新完成\n                ;\n                // 显示成功提示\n                toast({\n                    title: \"更新成功\"\n                });\n            }\n        } catch (error) {\n            console.error(\"保存失败:\", error);\n            // 显示错误提示\n            toast({\n                title: \"保存失败\",\n                description: error instanceof Error ? error.message : \"保存时发生未知错误\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleCancel = ()=>{\n        setIsEditing(false);\n        setIsAdding(false);\n        setShowEditDialog(false);\n        setFormData({});\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            placeholder: \"搜索目录\",\n                                            value: searchValue,\n                                            onChange: (e)=>{\n                                                setSearchValue(e.target.value);\n                                                // 实时搜索：输入时自动触发搜索\n                                                if (e.target.value.trim()) {\n                                                    const pathsToExpand = getMatchingNodePaths(catalogTree, e.target.value);\n                                                    setExpandedNodes(pathsToExpand);\n                                                } else {\n                                                    setExpandedNodes([\n                                                        \"1\",\n                                                        \"2\"\n                                                    ]) // 恢复默认展开状态\n                                                    ;\n                                                }\n                                            },\n                                            onKeyDown: (e)=>{\n                                                if (e.key === \"Enter\") {\n                                                    handleSearch();\n                                                }\n                                            },\n                                            className: \"w-80\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: handleResetSearch,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"重置\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleExpandAll,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"展开全部\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleCollapseAll,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"折叠全部\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        onClick: handleAdd,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"新增一级产品目录\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0\",\n                    children: categoriesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-muted-foreground\",\n                                children: \"加载中...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 13\n                    }, this) : categoriesError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm mb-2\",\n                                children: [\n                                    \"加载失败: \",\n                                    categoriesError\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: refetchCategories,\n                                children: \"重试\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm border-separate border-spacing-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"bg-muted/30 border-b\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"名称 / 英文名\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-32 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"描述\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-20 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"SKU数量\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"分类编码\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"报关编码\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"属性栏目\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-32 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"更新时间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground\",\n                                                children: \"操作\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: filteredCatalogTree.length > 0 ? filteredCatalogTree.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableTreeNode, {\n                                            node: node,\n                                            level: 0,\n                                            expandedNodes: expandedNodes,\n                                            onToggleExpanded: toggleExpanded,\n                                            onEdit: handleEditNode,\n                                            onDelete: handleDeleteNode,\n                                            onAddChild: handleAddChild,\n                                            searchTerm: searchValue\n                                        }, node.id, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 23\n                                        }, this)) : searchValue.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: 8,\n                                            className: \"p-8 text-center text-muted-foreground\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-8 h-8 text-muted-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"未找到匹配的目录\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: \"请尝试其他关键词或检查拼写\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: 8,\n                                            className: \"p-8 text-center text-muted-foreground\",\n                                            children: \"暂无数据\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 473,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 472,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showEditDialog,\n                onOpenChange: setShowEditDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"max-w-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                children: isAdding ? selectedNode ? \"添加\".concat([\n                                    \"二\",\n                                    \"三\",\n                                    \"四\",\n                                    \"五\",\n                                    \"六\",\n                                    \"七\"\n                                ][selectedNodeLevel], \"级产品目录\") : \"添加一级产品目录\" : \"编辑产品目录\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CatalogForm, {\n                            formData: formData,\n                            setFormData: setFormData,\n                            isEditing: true,\n                            isEditingExisting: !isAdding,\n                            onSave: handleSave,\n                            onCancel: handleCancel,\n                            loading: operationLoading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 542,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 541,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n        lineNumber: 420,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCatalogPage, \"90os8Ia1Am+c2vsT027XTEd1NRM=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_11__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast,\n        _hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategoryTree,\n        _hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategories\n    ];\n});\n_c = ProductCatalogPage;\n// 表格树节点组件\nfunction TableTreeNode(param) {\n    let { node, level, expandedNodes, onToggleExpanded, onEdit, onDelete, onAddChild, searchTerm = \"\" } = param;\n    var _node_children;\n    const hasChildren = node.children && node.children.length > 0;\n    const isExpanded = expandedNodes.includes(node.id);\n    // 高亮显示匹配的文本\n    const highlightText = (text, searchTerm)=>{\n        if (!searchTerm.trim()) return text;\n        const regex = new RegExp(\"(\".concat(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \")\"), \"gi\");\n        const parts = text.split(regex);\n        return parts.map((part, index)=>regex.test(part) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n                className: \"bg-yellow-200 text-yellow-900 px-1 rounded\",\n                children: part\n            }, index, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 601,\n                columnNumber: 9\n            }, this) : part);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            style: {\n                                paddingLeft: \"\".concat(level * 20, \"px\")\n                            },\n                            children: [\n                                hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>onToggleExpanded(node.id),\n                                    className: \"p-0.5 hover:bg-muted rounded\",\n                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-foreground\",\n                                            children: highlightText(node.name, searchTerm)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: highlightText(node.englishName, searchTerm)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 612,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 611,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground truncate max-w-32\",\n                            title: node.description,\n                            children: node.description || \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 643,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: \"outline\",\n                            className: \"text-xs\",\n                            children: node.categoryCode ? highlightText(node.categoryCode, searchTerm) : \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-1\",\n                            children: node.tags.length > 0 ? node.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"text-xs\",\n                                    children: tag\n                                }, index, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 17\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"--\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 654,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 653,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: node.createdAt\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 666,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onEdit(node),\n                                    className: \"h-7 px-2 text-xs text-blue-600 hover:text-blue-800\",\n                                    children: \"编辑\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 13\n                                }, this),\n                                level < 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onAddChild(node, level),\n                                    className: \"h-7 px-2 text-xs text-green-600 hover:text-green-800\",\n                                    children: [\n                                        \"新增\",\n                                        [\n                                            \"二\",\n                                            \"三\",\n                                            \"四\",\n                                            \"五\",\n                                            \"六\",\n                                            \"七\"\n                                        ][level],\n                                        \"级目录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onDelete(node),\n                                    className: \"h-7 px-2 text-xs text-red-600 hover:text-red-800\",\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 691,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 671,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 610,\n                columnNumber: 7\n            }, this),\n            hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: (_node_children = node.children) === null || _node_children === void 0 ? void 0 : _node_children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableTreeNode, {\n                        node: child,\n                        level: level + 1,\n                        expandedNodes: expandedNodes,\n                        onToggleExpanded: onToggleExpanded,\n                        onEdit: onEdit,\n                        onDelete: onDelete,\n                        onAddChild: onAddChild,\n                        searchTerm: searchTerm\n                    }, child.id, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 706,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false)\n        ]\n    }, void 0, true);\n}\n_c1 = TableTreeNode;\n// 分类表单组件\nfunction CatalogForm(param) {\n    let { formData, setFormData, isEditing, isEditingExisting = false, onSave, onCancel, loading } = param;\n    const availableTags = [\n        \"color\",\n        \"size\",\n        \"model\",\n        \"material\",\n        \"style\"\n    ];\n    const updateFormData = (field, value)=>{\n        setFormData({\n            ...formData,\n            [field]: value\n        });\n    };\n    if (isEditing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"name\",\n                                    children: [\n                                        \"中文名称 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 40\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"name\",\n                                    value: formData.name || \"\",\n                                    onChange: (e)=>updateFormData(\"name\", e.target.value),\n                                    placeholder: \"请输入中文名称\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 752,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"englishName\",\n                                    children: [\n                                        \"英文名称 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 47\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 763,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"englishName\",\n                                    value: formData.englishName || \"\",\n                                    onChange: (e)=>updateFormData(\"englishName\", e.target.value),\n                                    placeholder: \"请输入英文名称\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 764,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 762,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 751,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"categoryCode\",\n                                    children: [\n                                        \"分类编码 \",\n                                        isEditingExisting ? \"(不可修改)\" : \"(可选)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 776,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"categoryCode\",\n                                    value: formData.categoryCode || \"\",\n                                    onChange: (e)=>{\n                                        if (!isEditingExisting) {\n                                            // 只有在创建新分类时才允许修改\n                                            const value = e.target.value.toUpperCase().replace(/[^A-Z0-9_-]/g, \"\");\n                                            updateFormData(\"categoryCode\", value);\n                                        }\n                                    },\n                                    placeholder: isEditingExisting ? \"分类编码创建后不可修改\" : \"如：CLOTHING\",\n                                    disabled: isEditingExisting,\n                                    className: isEditingExisting ? \"bg-muted cursor-not-allowed\" : \"\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 779,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: isEditingExisting ? \"分类编码在创建后不能修改，以确保数据一致性\" : \"用于自动生成SKU，支持字母数字下划线连字符，会自动转换为大写\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 793,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 775,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"status\",\n                                    children: \"状态\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 801,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                    value: formData.status || \"active\",\n                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                placeholder: \"选择状态\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"active\",\n                                                    children: \"启用\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"inactive\",\n                                                    children: \"禁用\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 802,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 800,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 774,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                            htmlFor: \"description\",\n                            children: \"描述\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 818,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                            id: \"description\",\n                            value: formData.description || \"\",\n                            onChange: (e)=>updateFormData(\"description\", e.target.value),\n                            placeholder: \"请输入目录描述\",\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 819,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 817,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                            htmlFor: \"tags\",\n                            children: \"属性标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 829,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                            id: \"tags\",\n                            value: Array.isArray(formData.tags) ? formData.tags.join(\", \") : \"\",\n                            onChange: (e)=>updateFormData(\"tags\", e.target.value.split(\",\").map((t)=>t.trim()).filter((t)=>t)),\n                            placeholder: \"color, size\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 830,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: [\n                                \"可选标签: \",\n                                availableTags.join(\", \")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 836,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 828,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                            id: \"autoSku\",\n                            checked: !!formData.autoSku,\n                            onCheckedChange: (checked)=>updateFormData(\"autoSku\", !!checked)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 842,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                            htmlFor: \"autoSku\",\n                            children: \"自动生成SKU\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 847,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 841,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-end gap-2 pt-4 border-t\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            onClick: onCancel,\n                            disabled: loading,\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 852,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            onClick: onSave,\n                            disabled: loading,\n                            children: loading ? \"保存中...\" : \"确定\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 860,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 851,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n            lineNumber: 750,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n_c2 = CatalogForm;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ProductCatalogPage\");\n$RefreshReg$(_c1, \"TableTreeNode\");\n$RefreshReg$(_c2, \"CatalogForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/products/product-catalog-page.tsx\n"));

/***/ })

});