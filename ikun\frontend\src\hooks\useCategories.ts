/**
 * Categories Hooks
 * 分类相关的React Hooks
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { categoriesApi } from '@/lib/api';
import { ProductCategory, PaginatedResponse } from '@/types';

// 分类列表Hook
export function useCategories(params?: {
  level?: number;
  parent_id?: number;
  status?: string;
  autoFetch?: boolean; // 是否自动获取数据，默认为true
}) {
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0
  });

  const fetchCategories = useCallback(async (searchParams?: any) => {
    // 防止重复请求
    if (loading) return;

    setLoading(true);
    setError(null);

    try {
      const response = await categoriesApi.getCategories({
        page: 1,
        limit: 100, // 分类通常不会太多，可以一次性加载更多
        ...params,
        ...searchParams
      });

      setCategories(response.items);
      setPagination({
        total: response.total,
        page: response.page,
        limit: response.limit,
        totalPages: response.totalPages
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取分类列表失败');
      setCategories([]);
    } finally {
      setLoading(false);
    }
  }, [params]); // 移除loading依赖项，避免无限循环

  // 创建分类
  const createCategory = useCallback(async (categoryData: Partial<ProductCategory>) => {
    setLoading(true);
    setError(null);
    
    try {
      const newCategory = await categoriesApi.createCategory(categoryData);
      setCategories(prev => [newCategory, ...prev]);
      return newCategory;
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建分类失败');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // 更新分类
  const updateCategory = useCallback(async (id: number, categoryData: Partial<ProductCategory>) => {
    setLoading(true);
    setError(null);
    
    try {
      const updatedCategory = await categoriesApi.updateCategory(id, categoryData);
      setCategories(prev => prev.map(c => c.id === id ? updatedCategory : c));
      return updatedCategory;
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新分类失败');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // 删除分类
  const deleteCategory = useCallback(async (id: number) => {
    setLoading(true);
    setError(null);
    
    try {
      await categoriesApi.deleteCategory(id);
      setCategories(prev => prev.filter(c => c.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除分类失败');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // 更新分类状态
  const updateCategoryStatus = useCallback(async (id: number, status: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const updatedCategory = await categoriesApi.updateCategoryStatus(id, status);
      setCategories(prev => prev.map(c => c.id === id ? updatedCategory : c));
      return updatedCategory;
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新分类状态失败');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    // 只有在 autoFetch 为 true（默认）时才自动获取数据
    if (params?.autoFetch !== false) {
      fetchCategories();
    }
  }, [fetchCategories, params?.autoFetch]);

  return {
    categories,
    loading,
    error,
    pagination,
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    updateCategoryStatus,
    refetch: fetchCategories
  };
}

// 分类树Hook
export function useCategoryTree() {
  const [categoryTree, setCategoryTree] = useState<ProductCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const isRequestingRef = useRef(false);
  const hasInitializedRef = useRef(false);

  const fetchCategoryTree = useCallback(async () => {
    // 防止重复请求
    if (isRequestingRef.current) {
      return;
    }

    isRequestingRef.current = true;
    setLoading(true);
    setError(null);

    try {
      const tree = await categoriesApi.getCategoryTree();
      setCategoryTree(tree);
      hasInitializedRef.current = true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取分类树失败';
      setError(errorMessage);
      setCategoryTree([]);
    } finally {
      setLoading(false);
      isRequestingRef.current = false;
    }
  }, []);

  useEffect(() => {
    // 只在未初始化时执行一次
    if (!hasInitializedRef.current && !isRequestingRef.current) {
      fetchCategoryTree();
    }
  }, []); // 空依赖数组，只在组件挂载时执行一次

  return {
    categoryTree,
    loading,
    error,
    refetch: fetchCategoryTree
  };
}

// 指定层级分类Hook
export function useCategoriesByLevel(level: number) {
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategoriesByLevel = useCallback(async (categoryLevel: number) => {
    setLoading(true);
    setError(null);
    
    try {
      const categoriesData = await categoriesApi.getCategoriesByLevel(categoryLevel);
      setCategories(categoriesData);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取指定层级分类失败');
      setCategories([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (level) {
      fetchCategoriesByLevel(level);
    }
  }, [level, fetchCategoriesByLevel]);

  return {
    categories,
    loading,
    error,
    refetch: () => fetchCategoriesByLevel(level)
  };
}

// 单个分类Hook
export function useCategory(id: number | null) {
  const [category, setCategory] = useState<ProductCategory | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategory = useCallback(async (categoryId: number) => {
    setLoading(true);
    setError(null);
    
    try {
      const categoryData = await categoriesApi.getCategory(categoryId);
      setCategory(categoryData);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取分类详情失败');
      setCategory(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (id) {
      fetchCategory(id);
    }
  }, [id, fetchCategory]);

  return {
    category,
    loading,
    error,
    refetch: id ? () => fetchCategory(id) : undefined
  };
}

// 子分类Hook
export function useCategoryChildren(parentId: number | null) {
  const [children, setChildren] = useState<ProductCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchChildren = useCallback(async (id: number) => {
    setLoading(true);
    setError(null);
    
    try {
      const childrenData = await categoriesApi.getCategoryChildren(id);
      setChildren(childrenData);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取子分类失败');
      setChildren([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (parentId) {
      fetchChildren(parentId);
    }
  }, [parentId, fetchChildren]);

  return {
    children,
    loading,
    error,
    refetch: parentId ? () => fetchChildren(parentId) : undefined
  };
}
