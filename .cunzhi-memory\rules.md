# 开发规范和规则

- Worten产品编辑表单数据保存问题已修复：1.扩展后端uploadproductModel.updateListing方法支持所有表单字段 2.修正字段名映射multi_selling_point(数据库)↔multi_selling_points(前端) 3.前端表单提交时自动转换selling_point数组为multi_selling_points对象 4.更新查询方法返回完整字段数据
- 修复Worten产品编辑表单验证问题：1.创建动态验证模式，编辑模式下多语言字段不强制必填 2.新增模式保持严格验证，编辑模式使用宽松验证 3.表单提交时手动验证并显示错误 4.动态显示必填字段标记
- 修复Worten产品编辑表单三个问题：1.英文标题描述保存问题-修改后端updateListing服务分离产品基本信息和上架信息，同时更新product_dropship和uploadproduct_listings两个表 2.多语言卖点翻译按钮-在upload-product-form中为多语言卖点字段添加TranslationButton组件 3.翻译双请求问题-OPTIONS+POST是正常CORS预检请求，非问题
- 修复产品编辑表单数据回显问题：1.修改uploadproductModel.getListingById方法添加LEFT JOIN product_dropship表获取完整产品信息 2.修改uploadproductService.getListings方法同样添加表关联查询 3.更新WHERE条件使用表别名l和p 4.正确解析selling_point等JSON字段 5.确保编辑表单能显示来自两个表的完整数据
- 产品上架表冗余字段设计原则：1.保留sku/ean/english_title/image1冗余字段用于性能优化 2.实现数据同步机制-更新product_dropship时自动同步到uploadproduct_listings冗余字段 3.查询时使用COALESCE优先显示product_dropship数据，fallback到冗余字段 4.不删除冗余字段，通过同步保证数据一致性
- 修复编辑表单数据填充问题：前端upload-product-form组件编辑模式下错误地将english_description和image2-5字段设为空字符串，修改为使用后端JOIN查询返回的完整数据，包括english_description、selling_point、image1-5等字段，确保编辑时正确显示来自product_dropship表的数据
- 多语言卖点字段存储规则：1.英文卖点(selling_point)只存储在product_dropship表中 2.多语言卖点(multi_selling_point)只存储非英文的目标语言卖点 3.表单提交时不再将英文卖点添加到multi_selling_points对象中 4.保持基础数据(英文)和翻译数据(其他语言)的清晰分离
- 修复产品认领时描述字段不显示问题：在selectDropshipProduct函数中添加english_description字段的设置，确保认领产品时能正确显示和填充描述字段
- 系统设置字段重新设计：1.系统基础配置只保留company_name/system_title，新增代理相关字段 2.翻译服务提供商添加globally_enabled字段 3.重新设计翻译场景配置按平台维度配置三个场景(form_editing/batch_translation/translation_task)针对三种内容(title/description/selling_point) 4.删除translation_defaults 5.新增token_statistics统计配置
- 翻译设置页面需要完整编辑功能：1.DeepSeek火山引擎多模型/单模型配置的增删改 2.OpenAI兼容服务提供商的增删改 3.翻译场景配置可选择不同翻译服务(MTran/DeepSeek多模型或单模型/OpenAI兼容具体提供商) 4.使用模态框进行编辑，包含表单验证和确认对话框
- 翻译场景配置需要支持三级选择：1.翻译提供商选择(MTran/DeepSeek/OpenAI兼容) 2.子服务选择(DeepSeek需选择具体模型，OpenAI兼容需选择具体提供商，MTran无需子选择) 3.重新设计content_types字段为嵌套结构{provider,sub_service}
- 系统设置页面已优化为按需加载数据：主页面不再发送全局请求，各子页面独立请求自己需要的数据，翻译设置页面支持标签页切换时按需加载，避免重复请求问题- TranslationService优化方案：使用懒加载+配置热更新机制，延迟实例化到真正需要时，确保数据库配置优先级正确
- 产品目录分类层级已从3级升级到7级：1.后端类型定义和数据库模型支持1-7级分类 2.前端界面支持创建和管理7级分类结构 3.分类选择器改为选择叶子节点而非固定层级 4.验证规则已更新支持1-7级 5.UI界面优化支持更深层级显示
