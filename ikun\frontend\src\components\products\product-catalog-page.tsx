'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useConfirm } from '@/components/ui/confirm-dialog'
import { useToast } from '@/hooks/use-toast'
import {
  Plus,
  Edit,
  Trash2,
  Search,
  ChevronRight,
  ChevronDown,
  X,
  Expand,
  Minimize2
} from 'lucide-react'
import { useCategoryTree, useCategories } from '@/hooks/useCategories'
import { ProductCategory } from '@/types'

// 模拟树状目录数据
interface CatalogNode {
  id: string
  name: string
  englishName: string
  categoryCode: string
  description: string
  tags: string[]
  autoSku: boolean
  createdAt: string
  status: 'active' | 'inactive'
  children?: CatalogNode[]
}



export function ProductCatalogPage() {
  const [expandedNodes, setExpandedNodes] = useState<string[]>(['1', '2'])
  const [selectedNode, setSelectedNode] = useState<CatalogNode | null>(null)
  const [selectedNodeLevel, setSelectedNodeLevel] = useState<number>(0) // 存储选中节点的层级
  const [searchValue, setSearchValue] = useState('')
  const [isEditing, setIsEditing] = useState(false)
  const [isAdding, setIsAdding] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [formData, setFormData] = useState<Partial<CatalogNode>>({})

  const { confirm } = useConfirm()
  const { toast } = useToast()

  // 使用API hooks
  const {
    categoryTree: apiCategoryTree,
    loading: categoriesLoading,
    error: categoriesError,
    refetch: refetchCategories
  } = useCategoryTree();

  const {
    createCategory,
    updateCategory,
    deleteCategory,
    loading: operationLoading
  } = useCategories();

  // 转换API数据为显示格式
  const convertCategoryToNode = (category: ProductCategory): CatalogNode => ({
    id: category.id.toString(),
    name: category.chinese_name,
    englishName: category.english_name,
    categoryCode: category.category_code,
    description: category.category_description || '',
    tags: category.attribute_tags || [],
    autoSku: category.auto_sku === 'enabled',
    createdAt: new Date(category.created_at).toLocaleString('zh-CN'),
    status: category.status === 'enabled' ? 'active' : 'inactive',
    children: category.children ? category.children.map(convertCategoryToNode) : undefined
  });

  const catalogTree = apiCategoryTree ? apiCategoryTree.map(convertCategoryToNode) : [];

  const toggleExpanded = (nodeId: string) => {
    setExpandedNodes(prev =>
      prev.includes(nodeId)
        ? prev.filter(id => id !== nodeId)
        : [...prev, nodeId]
    )
  }

  // 递归获取所有有子节点的节点ID
  const getAllExpandableNodeIds = (nodes: CatalogNode[]): string[] => {
    const ids: string[] = []

    const traverse = (nodeList: CatalogNode[]) => {
      nodeList.forEach(node => {
        if (node.children && node.children.length > 0) {
          ids.push(node.id)
          traverse(node.children)
        }
      })
    }

    traverse(nodes)
    return ids
  }

  // 展开全部
  const handleExpandAll = () => {
    const allExpandableIds = getAllExpandableNodeIds(catalogTree)
    setExpandedNodes(allExpandableIds)
  }

  // 折叠全部
  const handleCollapseAll = () => {
    setExpandedNodes([])
  }

  // 递归搜索匹配的节点
  const searchNodes = (nodes: CatalogNode[], searchTerm: string): CatalogNode[] => {
    if (!searchTerm.trim()) return nodes

    const results: CatalogNode[] = []
    const lowerSearchTerm = searchTerm.toLowerCase()

    const searchInNode = (node: CatalogNode): CatalogNode | null => {
      // 检查当前节点是否匹配
      const nameMatch = node.name.toLowerCase().includes(lowerSearchTerm)
      const englishNameMatch = node.englishName.toLowerCase().includes(lowerSearchTerm)
      const codeMatch = node.categoryCode?.toLowerCase().includes(lowerSearchTerm)

      const isCurrentMatch = nameMatch || englishNameMatch || codeMatch

      // 递归搜索子节点
      const matchingChildren: CatalogNode[] = []
      if (node.children) {
        node.children.forEach(child => {
          const childResult = searchInNode(child)
          if (childResult) {
            matchingChildren.push(childResult)
          }
        })
      }

      // 如果当前节点匹配或有匹配的子节点，则返回节点
      if (isCurrentMatch || matchingChildren.length > 0) {
        return {
          ...node,
          children: matchingChildren.length > 0 ? matchingChildren : node.children
        }
      }

      return null
    }

    nodes.forEach(node => {
      const result = searchInNode(node)
      if (result) {
        results.push(result)
      }
    })

    return results
  }

  // 获取搜索结果中所有匹配节点的路径（用于自动展开）
  const getMatchingNodePaths = (nodes: CatalogNode[], searchTerm: string): string[] => {
    if (!searchTerm.trim()) return []

    const paths: string[] = []
    const lowerSearchTerm = searchTerm.toLowerCase()

    const findPaths = (nodeList: CatalogNode[], currentPath: string[] = []) => {
      nodeList.forEach(node => {
        const newPath = [...currentPath, node.id]

        // 检查当前节点是否匹配
        const nameMatch = node.name.toLowerCase().includes(lowerSearchTerm)
        const englishNameMatch = node.englishName.toLowerCase().includes(lowerSearchTerm)
        const codeMatch = node.categoryCode?.toLowerCase().includes(lowerSearchTerm)

        if (nameMatch || englishNameMatch || codeMatch) {
          // 添加到匹配节点的所有父节点路径
          newPath.slice(0, -1).forEach(parentId => {
            if (!paths.includes(parentId)) {
              paths.push(parentId)
            }
          })
        }

        // 递归搜索子节点
        if (node.children && node.children.length > 0) {
          findPaths(node.children, newPath)
        }
      })
    }

    findPaths(nodes)
    return paths
  }

  // 处理搜索
  const handleSearch = () => {
    if (!searchValue.trim()) {
      // 如果搜索框为空，重置展开状态
      setExpandedNodes(['1', '2']) // 恢复默认展开状态
      return
    }

    // 获取匹配节点的父节点路径并自动展开
    const pathsToExpand = getMatchingNodePaths(catalogTree, searchValue)
    setExpandedNodes(pathsToExpand)
  }

  // 重置搜索
  const handleResetSearch = () => {
    setSearchValue('')
  }

  // 过滤后的目录树（用于显示搜索结果）
  const filteredCatalogTree = searchValue.trim()
    ? searchNodes(catalogTree, searchValue)
    : catalogTree



  const handleAdd = () => {
    setIsAdding(true)
    setIsEditing(false)
    setSelectedNode(null)
    setSelectedNodeLevel(0) // 一级分类的层级为0
    setShowEditDialog(true)
    setFormData({
      name: '',
      englishName: '',
      categoryCode: '',
      description: '',
      tags: [],
      autoSku: false,
      status: 'active'
    })
  }

  const handleEditNode = (node: CatalogNode) => {
    setSelectedNode(node)
    setIsEditing(true)
    setIsAdding(false)
    setShowEditDialog(true)
    setFormData({
      name: node.name,
      englishName: node.englishName,
      categoryCode: node.categoryCode,
      description: node.description,
      tags: node.tags,
      autoSku: node.autoSku,
      status: node.status
    })
  }

  const handleDeleteNode = async (node: CatalogNode) => {
    const confirmed = await confirm({
      title: '删除分类',
      description: `确定要删除分类"${node.name}"吗？此操作不可撤销。`,
      confirmText: '删除',
      cancelText: '取消',
      variant: 'destructive'
    })

    if (confirmed) {
      try {
        await deleteCategory(parseInt(node.id))
        refetchCategories()

        // 显示删除成功提示
        toast({
          title: `删除成功`,
        })
      } catch (error) {
        console.error('删除失败:', error)
        // 显示删除失败提示
        toast({
          title: "删除失败",
          description: error instanceof Error ? error.message : "删除时发生未知错误",
          variant: "destructive"
        })
      }
    }
  }

  const handleAddChild = (parentNode: CatalogNode, parentLevel: number) => {
    setIsAdding(true)
    setIsEditing(false)
    setSelectedNode(parentNode)
    setSelectedNodeLevel(parentLevel) // 存储父节点的层级
    setShowEditDialog(true)
    setFormData({
      name: '',
      englishName: '',
      categoryCode: '',
      description: '',
      tags: [],
      autoSku: false,
      status: 'active'
    })
  }

  const handleSave = async () => {
    try {
      // 验证必填字段
      if (!formData.name?.trim()) {
        toast({
          title: "验证失败",
          description: "请输入中文名称",
          variant: "destructive"
        })
        return
      }

      if (!formData.englishName?.trim()) {
        toast({
          title: "验证失败",
          description: "请输入英文名称",
          variant: "destructive"
        })
        return
      }

      if (isAdding) {
        // 创建新分类
        const categoryCode = formData.categoryCode?.trim()

        // 根据是否有选中的父节点来确定层级和父ID
        let categoryLevel: 1 | 2 | 3 | 4 | 5 | 6 | 7 = 1
        let parentId: number | null = null

        if (selectedNode) {
          // 如果有选中的父节点，说明是添加子分类
          parentId = parseInt(selectedNode.id)
          // 根据父节点的层级确定子节点层级
          // selectedNodeLevel: 0=一级, 1=二级, 2=三级, 3=四级, 4=五级, 5=六级 (前端显示层级)
          // category_level: 1=一级, 2=二级, 3=三级, 4=四级, 5=五级, 6=六级, 7=七级 (后端数据库层级)
          // 子分类的层级 = 父分类的数据库层级 + 1 = (selectedNodeLevel + 1) + 1
          categoryLevel = (selectedNodeLevel + 2) as 1 | 2 | 3 | 4 | 5 | 6 | 7
        }

        const createData = {
          // 分类编码处理：如果有值则转换为大写，如果为空则不传递该字段
          ...(categoryCode ? { category_code: categoryCode.toUpperCase() } : {}),
          chinese_name: formData.name.trim(),
          english_name: formData.englishName.trim(),
          status: (formData.status === 'active' ? 'enabled' : 'disabled') as 'enabled' | 'disabled',
          auto_sku: (formData.autoSku ? 'enabled' : 'disabled') as 'enabled' | 'disabled',
          category_level: categoryLevel,
          parent_id: parentId,
          category_description: formData.description?.trim() || '',
          attribute_tags: formData.tags || [],
          sort_order: 0
        }



        await createCategory(createData)
        setIsAdding(false)
        setShowEditDialog(false)
        setFormData({}) // 清空表单数据
        await refetchCategories() // 等待刷新完成

        // 显示成功提示
        toast({
          title: "创建成功",
        })
      } else if (selectedNode && isEditing) {
        // 更新分类 - 编辑时不允许修改分类编码
        const updateData = {
          chinese_name: formData.name.trim(),
          english_name: formData.englishName.trim(),
          status: (formData.status === 'active' ? 'enabled' : 'disabled') as 'enabled' | 'disabled',
          auto_sku: (formData.autoSku ? 'enabled' : 'disabled') as 'enabled' | 'disabled',
          category_description: formData.description?.trim() || '',
          attribute_tags: formData.tags || []
        }

        await updateCategory(parseInt(selectedNode.id), updateData)
        setIsEditing(false)
        setShowEditDialog(false)
        await refetchCategories() // 等待刷新完成

        // 显示成功提示
        toast({
          title: "更新成功",
        })
      }
    } catch (error) {
      console.error('保存失败:', error)
      // 显示错误提示
      toast({
        title: "保存失败",
        description: error instanceof Error ? error.message : "保存时发生未知错误",
        variant: "destructive"
      })
    }
  }

  const handleCancel = () => {
    setIsEditing(false)
    setIsAdding(false)
    setShowEditDialog(false)
    setFormData({})
  }



  return (
    <div className="space-y-4">
      {/* 顶部操作区域 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Input
                  placeholder="搜索目录"
                  value={searchValue}
                  onChange={(e) => {
                    setSearchValue(e.target.value)
                    // 实时搜索：输入时自动触发搜索
                    if (e.target.value.trim()) {
                      const pathsToExpand = getMatchingNodePaths(catalogTree, e.target.value)
                      setExpandedNodes(pathsToExpand)
                    } else {
                      setExpandedNodes(['1', '2']) // 恢复默认展开状态
                    }
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch()
                    }
                  }}
                  className="w-80"
                />
                <Button size="sm" variant="outline" onClick={handleResetSearch}>
                  <X className="w-4 h-4 mr-1" />
                  重置
                </Button>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button size="sm" variant="outline" onClick={handleExpandAll}>
                <Expand className="w-4 h-4 mr-1" />
                展开全部
              </Button>
              <Button size="sm" variant="outline" onClick={handleCollapseAll}>
                <Minimize2 className="w-4 h-4 mr-1" />
                折叠全部
              </Button>
              <Button size="sm" onClick={handleAdd}>
                <Plus className="w-4 h-4 mr-1" />
                新增一级产品目录
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 表格区域 */}
      <Card>
        <CardContent className="p-0">
          {categoriesLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-sm text-muted-foreground">加载中...</span>
            </div>
          ) : categoriesError ? (
            <div className="text-center py-8">
              <p className="text-red-600 text-sm mb-2">加载失败: {categoriesError}</p>
              <Button size="sm" variant="outline" onClick={refetchCategories}>
                重试
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-sm border-separate border-spacing-0">
                <thead>
                  <tr className="bg-muted/30 border-b">
                    <th className="p-4 text-left font-medium text-muted-foreground border-r border-border/50">名称 / 英文名</th>
                    <th className="w-32 p-4 text-left font-medium text-muted-foreground border-r border-border/50">描述</th>
                    <th className="w-20 p-4 text-left font-medium text-muted-foreground border-r border-border/50">SKU数量</th>
                    <th className="w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50">分类编码</th>
                    <th className="w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50">报关编码</th>
                    <th className="w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50">属性栏目</th>
                    <th className="w-32 p-4 text-left font-medium text-muted-foreground border-r border-border/50">更新时间</th>
                    <th className="w-24 p-4 text-left font-medium text-muted-foreground">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredCatalogTree.length > 0 ? (
                    filteredCatalogTree.map((node) => (
                      <TableTreeNode
                        key={node.id}
                        node={node}
                        level={0}
                        expandedNodes={expandedNodes}
                        onToggleExpanded={toggleExpanded}
                        onEdit={handleEditNode}
                        onDelete={handleDeleteNode}
                        onAddChild={handleAddChild}
                        searchTerm={searchValue} // 传递搜索词用于高亮显示
                      />
                    ))
                  ) : searchValue.trim() ? (
                    <tr>
                      <td colSpan={8} className="p-8 text-center text-muted-foreground">
                        <div className="flex flex-col items-center gap-2">
                          <Search className="w-8 h-8 text-muted-foreground/50" />
                          <p>未找到匹配的目录</p>
                          <p className="text-sm">请尝试其他关键词或检查拼写</p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    <tr>
                      <td colSpan={8} className="p-8 text-center text-muted-foreground">
                        暂无数据
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 编辑对话框 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {isAdding
                ? (selectedNode
                    ? `添加${['二', '三', '四', '五', '六', '七'][selectedNodeLevel]}级产品目录`
                    : '添加一级产品目录'
                  )
                : '编辑产品目录'
              }
            </DialogTitle>
          </DialogHeader>
          <CatalogForm
            formData={formData}
            setFormData={setFormData}
            isEditing={true}
            isEditingExisting={!isAdding} // 如果不是添加模式，就是编辑现有分类
            onSave={handleSave}
            onCancel={handleCancel}
            loading={operationLoading}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}

// 表格树节点组件
function TableTreeNode({
  node,
  level,
  expandedNodes,
  onToggleExpanded,
  onEdit,
  onDelete,
  onAddChild,
  searchTerm = ''
}: {
  node: CatalogNode
  level: number
  expandedNodes: string[]
  onToggleExpanded: (nodeId: string) => void
  onEdit: (node: CatalogNode) => void
  onDelete: (node: CatalogNode) => void
  onAddChild: (node: CatalogNode, level: number) => void
  searchTerm?: string
}) {
  const hasChildren = node.children && node.children.length > 0
  const isExpanded = expandedNodes.includes(node.id)

  // 高亮显示匹配的文本
  const highlightText = (text: string, searchTerm: string) => {
    if (!searchTerm.trim()) return text

    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
    const parts = text.split(regex)

    return parts.map((part, index) =>
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 text-yellow-900 px-1 rounded">
          {part}
        </mark>
      ) : part
    )
  }

  return (
    <>
      <tr className="border-b border-border/50 hover:bg-muted/30 transition-colors">
        <td className="p-4 border-r border-border/50">
          <div className="flex items-center gap-2" style={{ paddingLeft: `${level * 20}px` }}>
            {hasChildren ? (
              <button
                onClick={() => onToggleExpanded(node.id)}
                className="p-0.5 hover:bg-muted rounded"
              >
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
              </button>
            ) : (
              <div className="w-5" />
            )}
            <div>
              <div className="font-medium text-foreground">
                {highlightText(node.name, searchTerm)}
              </div>
              <div className="text-xs text-muted-foreground">
                {highlightText(node.englishName, searchTerm)}
              </div>
            </div>
          </div>
        </td>
        <td className="p-4 border-r border-border/50">
          <div className="text-sm text-muted-foreground truncate max-w-32" title={node.description}>
            {node.description || '--'}
          </div>
        </td>
        <td className="p-4 border-r border-border/50 text-center">
          <span className="text-sm">0</span>
        </td>
        <td className="p-4 border-r border-border/50">
          <Badge variant="outline" className="text-xs">
            {node.categoryCode ? highlightText(node.categoryCode, searchTerm) : '--'}
          </Badge>
        </td>
        <td className="p-4 border-r border-border/50">
          <span className="text-sm text-muted-foreground">--</span>
        </td>
        <td className="p-4 border-r border-border/50">
          <div className="flex flex-wrap gap-1">
            {node.tags.length > 0 ? (
              node.tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))
            ) : (
              <span className="text-sm text-muted-foreground">--</span>
            )}
          </div>
        </td>
        <td className="p-4 border-r border-border/50">
          <div className="text-xs text-muted-foreground">
            {node.createdAt}
          </div>
        </td>
        <td className="p-4">
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onEdit(node)}
              className="h-7 px-2 text-xs text-blue-600 hover:text-blue-800"
            >
              编辑
            </Button>
            {level < 6 && ( // 只有1-6级分类才显示"新增子级目录"按钮（最大支持7级）
              <Button
                size="sm"
                variant="ghost"
                onClick={() => onAddChild(node, level)}
                className="h-7 px-2 text-xs text-green-600 hover:text-green-800"
              >
                新增{['二', '三', '四', '五', '六', '七'][level]}级目录
              </Button>
            )}
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onDelete(node)}
              className="h-7 px-2 text-xs text-red-600 hover:text-red-800"
            >
              删除
            </Button>
          </div>
        </td>
      </tr>

      {hasChildren && isExpanded && (
        <>
          {node.children?.map((child) => (
            <TableTreeNode
              key={child.id}
              node={child}
              level={level + 1}
              expandedNodes={expandedNodes}
              onToggleExpanded={onToggleExpanded}
              onEdit={onEdit}
              onDelete={onDelete}
              onAddChild={onAddChild}
              searchTerm={searchTerm}
            />
          ))}
        </>
      )}
    </>
  )
}

// 分类表单组件
function CatalogForm({
  formData,
  setFormData,
  isEditing,
  isEditingExisting = false,
  onSave,
  onCancel,
  loading
}: {
  formData: Partial<CatalogNode>,
  setFormData: (data: Partial<CatalogNode>) => void,
  isEditing: boolean,
  isEditingExisting?: boolean, // 是否在编辑现有分类（而不是创建新分类）
  onSave?: () => Promise<void>,
  onCancel?: () => void,
  loading?: boolean
}) {
  const availableTags = ['color', 'size', 'model', 'material', 'style']

  const updateFormData = (field: string, value: any) => {
    setFormData({ ...formData, [field]: value })
  }

  if (isEditing) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">中文名称 <span className="text-red-500">*</span></Label>
            <Input
              id="name"
              value={formData.name || ''}
              onChange={(e) => updateFormData('name', e.target.value)}
              placeholder="请输入中文名称"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="englishName">英文名称 <span className="text-red-500">*</span></Label>
            <Input
              id="englishName"
              value={formData.englishName || ''}
              onChange={(e) => updateFormData('englishName', e.target.value)}
              placeholder="请输入英文名称"
              required
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="categoryCode">
              分类编码 {isEditingExisting ? '(不可修改)' : '(可选)'}
            </Label>
            <Input
              id="categoryCode"
              value={formData.categoryCode || ''}
              onChange={(e) => {
                if (!isEditingExisting) {
                  // 只有在创建新分类时才允许修改
                  const value = e.target.value.toUpperCase().replace(/[^A-Z0-9_-]/g, '')
                  updateFormData('categoryCode', value)
                }
              }}
              placeholder={isEditingExisting ? "分类编码创建后不可修改" : "如：CLOTHING"}
              disabled={isEditingExisting}
              className={isEditingExisting ? "bg-muted cursor-not-allowed" : ""}
            />
            <div className="text-xs text-muted-foreground">
              {isEditingExisting
                ? "分类编码在创建后不能修改，以确保数据一致性"
                : "用于自动生成SKU，支持字母数字下划线连字符，会自动转换为大写"
              }
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="status">状态</Label>
            <Select
              value={formData.status || 'active'}
              onValueChange={(value) => updateFormData('status', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">启用</SelectItem>
                <SelectItem value="inactive">禁用</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">描述</Label>
          <Textarea
            id="description"
            value={formData.description || ''}
            onChange={(e) => updateFormData('description', e.target.value)}
            placeholder="请输入目录描述"
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="tags">属性标签</Label>
          <Input
            id="tags"
            value={Array.isArray(formData.tags) ? formData.tags.join(', ') : ''}
            onChange={(e) => updateFormData('tags', e.target.value.split(',').map(t => t.trim()).filter(t => t))}
            placeholder="color, size"
          />
          <div className="text-xs text-muted-foreground">
            可选标签: {availableTags.join(', ')}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="autoSku"
            checked={!!formData.autoSku}
            onCheckedChange={(checked) => updateFormData('autoSku', !!checked)}
          />
          <Label htmlFor="autoSku">自动生成SKU</Label>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-end gap-2 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            type="button"
            onClick={onSave}
            disabled={loading}
          >
            {loading ? '保存中...' : '确定'}
          </Button>
        </div>
      </div>
    )
  }

  return null
}


