"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/dropship/page",{

/***/ "(app-pages-browser)/./src/components/products/category-selector.tsx":
/*!*******************************************************!*\
  !*** ./src/components/products/category-selector.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategorySelector: function() { return /* binding */ CategorySelector; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_FolderTree_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,FolderTree,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-tree.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_FolderTree_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,FolderTree,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_FolderTree_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,FolderTree,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_FolderTree_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,FolderTree,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_FolderTree_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,FolderTree,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _hooks_useCategories__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useCategories */ \"(app-pages-browser)/./src/hooks/useCategories.ts\");\n/* __next_internal_client_entry_do_not_use__ CategorySelector auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// 将API数据转换为选择器格式\nconst convertCategoryToNode = function(category) {\n    let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, parentPath = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"\", parentDisplayPath = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"\";\n    const currentPath = parentPath ? \"\".concat(parentPath, \"/\").concat(category.english_name.toLowerCase()) : category.english_name.toLowerCase();\n    const currentDisplayPath = parentDisplayPath ? \"\".concat(parentDisplayPath, \"-\").concat(category.chinese_name, \"（\").concat(category.english_name, \"）\") : \"\".concat(category.chinese_name, \"（\").concat(category.english_name, \"）\");\n    const node = {\n        id: category.id.toString(),\n        name: category.chinese_name,\n        englishName: category.english_name,\n        level,\n        path: currentPath,\n        displayPath: currentDisplayPath\n    };\n    const result = [];\n    // 如果是七级分类，才可以选择（最深层级）\n    if (level === 7) {\n        result.push(node);\n    }\n    // 递归处理子分类\n    if (category.children && category.children.length > 0) {\n        const childNodes = category.children.flatMap((child)=>convertCategoryToNode(child, level + 1, currentPath, currentDisplayPath));\n        result.push(...childNodes);\n    }\n    return result;\n};\n// 扁平化所有可选择的分类（只有三级分类可选）\nconst flattenCategories = (categories)=>{\n    return categories.flatMap((cat)=>convertCategoryToNode(cat));\n};\nfunction CategorySelector(param) {\n    let { value, onValueChange, placeholder = \"选择产品类目...\", className, error = false } = param;\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 获取分类数据\n    const { categoryTree: apiCategoryTree, loading: categoriesLoading, error: categoriesError } = (0,_hooks_useCategories__WEBPACK_IMPORTED_MODULE_6__.useCategoryTree)();\n    // 转换为可选择的分类列表\n    const selectableCategories = apiCategoryTree ? flattenCategories(apiCategoryTree) : [];\n    // 过滤分类（支持中文名和英文名搜索）\n    const filteredCategories = selectableCategories.filter((category)=>{\n        if (!searchValue.trim()) return true;\n        const searchTerm = searchValue.toLowerCase();\n        return category.name.toLowerCase().includes(searchTerm) || category.englishName.toLowerCase().includes(searchTerm) || category.displayPath.toLowerCase().includes(searchTerm);\n    });\n    // 获取当前选中分类的显示信息\n    const selectedCategory = selectableCategories.find((cat)=>cat.path === value);\n    const handleSelect = (category)=>{\n        onValueChange(category.path, category.displayPath);\n        setOpen(false);\n        setSearchValue(\"\");\n    };\n    const handleClear = ()=>{\n        onValueChange(\"\", \"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.Popover, {\n        open: open,\n        onOpenChange: setOpen,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"outline\",\n                    role: \"combobox\",\n                    \"aria-expanded\": open,\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full justify-between\", !selectedCategory && \"text-muted-foreground\", error && \"border-red-500\", className),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_FolderTree_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"truncate\",\n                                    children: selectedCategory ? selectedCategory.displayPath : placeholder\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1 flex-shrink-0\",\n                            children: [\n                                selectedCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"h-4 w-4 p-0 hover:bg-transparent\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleClear();\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_FolderTree_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-3 h-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_FolderTree_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverContent, {\n                className: \"w-[600px] p-0\",\n                align: \"start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center border-b px-3 py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_FolderTree_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4 shrink-0 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    placeholder: \"搜索类目（支持中文名/英文名）...\",\n                                    value: searchValue,\n                                    onChange: (e)=>setSearchValue(e.target.value),\n                                    className: \"border-0 bg-transparent p-0 text-sm outline-none focus-visible:ring-0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-[300px] overflow-y-auto\",\n                            children: categoriesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 text-center text-sm text-muted-foreground\",\n                                children: \"加载中...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this) : categoriesError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 text-center text-sm text-red-500\",\n                                children: [\n                                    \"加载失败: \",\n                                    categoriesError\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, this) : filteredCategories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 text-center text-sm text-muted-foreground\",\n                                children: searchValue ? \"未找到匹配的类目\" : \"暂无可选择的类目\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-1\",\n                                children: filteredCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 p-3 cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-sm\",\n                                        onClick: ()=>handleSelect(category),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_FolderTree_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"mr-2 h-4 w-4\", value === category.path ? \"opacity-100\" : \"opacity-0\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium truncate\",\n                                                        children: category.displayPath\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: [\n                                                            \"路径: \",\n                                                            category.path\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, category.id, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\category-selector.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s(CategorySelector, \"XFSeLN0WxxgUuOgf3UtShmoBmww=\", false, function() {\n    return [\n        _hooks_useCategories__WEBPACK_IMPORTED_MODULE_6__.useCategoryTree\n    ];\n});\n_c = CategorySelector;\nvar _c;\n$RefreshReg$(_c, \"CategorySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/products/category-selector.tsx\n"));

/***/ })

});