/**
 * Product Categories Routes
 * Product category management endpoints
 */

import { Router } from 'express';
import { categoryController } from '@/controllers/products/categoryController';
import { validateRequest } from '@/middleware/validation/validateRequest';
import { categoryValidation } from '@/validations/products/categoryValidation';

const router = Router();

// GET /api/v1/categories - Get categories list
router.get('/',
  validateRequest({ query: categoryValidation.getCategories }),
  categoryController.getCategories.bind(categoryController)
);

// GET /api/v1/categories/getbytree - Get category tree structure
router.get('/getbytree', categoryController.getCategoryTree.bind(categoryController));

// GET /api/v1/categories/level/:level - Get categories by level
router.get('/level/:level',
  validateRequest({ params: categoryValidation.getByLevel }),
  categoryController.getCategoriesByLevel.bind(categoryController)
);

// POST /api/v1/categories - Create category
router.post('/',
  validateRequest({ body: categoryValidation.createCategory }),
  categoryController.createCategory.bind(categoryController)
);

// POST /api/v1/categories/batch - Batch create categories
router.post('/batch',
  validateRequest({ body: categoryValidation.batchCreate }),
  categoryController.batchCreateCategories.bind(categoryController)
);

// GET /api/v1/categories/:id - Get category details
router.get('/:id',
  validateRequest({ params: categoryValidation.categoryId }),
  categoryController.getCategoryById.bind(categoryController)
);

// PUT /api/v1/categories/:id - Update category
router.put('/:id',
  validateRequest({
    params: categoryValidation.categoryId,
    body: categoryValidation.updateCategory
  }),
  categoryController.updateCategory.bind(categoryController)
);

// PUT /api/v1/categories/:id/status - Update category status
router.put('/:id/status',
  validateRequest({
    params: categoryValidation.categoryId,
    body: categoryValidation.updateStatus
  }),
  categoryController.updateCategoryStatus.bind(categoryController)
);

// DELETE /api/v1/categories/:id - Delete category
router.delete('/:id',
  validateRequest({ params: categoryValidation.categoryId }),
  categoryController.deleteCategory.bind(categoryController)
);

// GET /api/v1/categories/:id/children - Get category children
router.get('/:id/children',
  validateRequest({ params: categoryValidation.categoryId }),
  categoryController.getCategoryChildren.bind(categoryController)
);

export default router;
