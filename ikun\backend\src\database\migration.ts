/**
 * Database Migration Manager
 * 自动创建数据表和管理字段变更
 */

import { database } from './connection';
import { logger } from '@/utils/logger';
import { seedDataManager } from './seedData';

// 数据表结构定义
export interface TableSchema {
  tableName: string;
  columns: ColumnDefinition[];
  indexes?: IndexDefinition[];
  foreignKeys?: ForeignKeyDefinition[];
}

export interface ColumnDefinition {
  name: string;
  type: string;
  nullable?: boolean;
  default?: string | number | null;
  autoIncrement?: boolean;
  primaryKey?: boolean;
  unique?: boolean;
  comment?: string;
}

export interface IndexDefinition {
  name: string;
  columns: string[];
  unique?: boolean;
}

export interface ForeignKeyDefinition {
  name: string;
  column: string;
  referencedTable: string;
  referencedColumn: string;
  onDelete?: 'CASCADE' | 'SET NULL' | 'RESTRICT';
  onUpdate?: 'CASCADE' | 'SET NULL' | 'RESTRICT';
}

// 数据表结构定义
const tableSchemas: TableSchema[] = [
  // 用户表
  {
    tableName: 'users',
    columns: [
      { name: 'id', type: 'BIGINT', autoIncrement: true, primaryKey: true },
      { name: 'username', type: 'VARCHAR(50)', nullable: false, unique: true },
      { name: 'email', type: 'VARCHAR(100)', nullable: false, unique: true },
      { name: 'password_hash', type: 'VARCHAR(255)', nullable: false },
      { name: 'role', type: "ENUM('admin', 'user', 'viewer')", default: "'user'" },
      { name: 'last_login', type: 'TIMESTAMP', nullable: true },
      { name: 'is_active', type: 'BOOLEAN', default: 'TRUE' },
      { name: 'created_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP' },
      { name: 'updated_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP' }
    ],
    indexes: [
      { name: 'idx_users_username', columns: ['username'] },
      { name: 'idx_users_email', columns: ['email'] },
      { name: 'idx_users_role', columns: ['role'] }
    ]
  },

  // 产品分类表
  {
    tableName: 'product_categories',
    columns: [
      { name: 'id', type: 'BIGINT', autoIncrement: true, primaryKey: true },
      { name: 'category_code', type: 'VARCHAR(50)', nullable: true, unique: true, comment: '分类编码' },
      { name: 'english_name', type: 'VARCHAR(200)', nullable: false },
      { name: 'chinese_name', type: 'VARCHAR(200)', nullable: false },
      { name: 'status', type: "ENUM('enabled', 'disabled')", default: "'enabled'" },
      { name: 'auto_sku', type: "ENUM('enabled', 'disabled')", default: "'disabled'" },
      { name: 'category_level', type: 'TINYINT', nullable: false, comment: '分类层级' },
      { name: 'parent_id', type: 'BIGINT', nullable: true },
      { name: 'category_description', type: 'TEXT', nullable: true },
      { name: 'attribute_tags', type: 'JSON', nullable: true, comment: '属性标签数组' },
      { name: 'category_path', type: 'VARCHAR(500)', nullable: true, comment: '分类路径' },
      { name: 'created_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP' },
      { name: 'updated_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP' }
    ],
    indexes: [
      { name: 'idx_categories_code', columns: ['category_code'] },
      { name: 'idx_categories_level', columns: ['category_level'] },
      { name: 'idx_categories_parent', columns: ['parent_id'] },
      { name: 'idx_categories_status', columns: ['status'] }
    ],
    foreignKeys: [
      {
        name: 'fk_categories_parent',
        column: 'parent_id',
        referencedTable: 'product_categories',
        referencedColumn: 'id',
        onDelete: 'CASCADE'
      }
    ]
  },

  // 铺货产品表
  {
    tableName: 'product_dropship',
    columns: [
      { name: 'id', type: 'BIGINT', autoIncrement: true, primaryKey: true },
      { name: 'source', type: 'VARCHAR(50)', nullable: false, comment: '产品来源' },
      { name: 'sku', type: 'VARCHAR(100)', nullable: false, unique: true },
      { name: 'ean', type: 'VARCHAR(20)', nullable: false, comment: '产品EAN码' },
      { name: 'category_id', type: 'BIGINT', nullable: true },
      { name: 'category', type: 'VARCHAR(500)', nullable: true, comment: '分类路径' },
      { name: 'english_title', type: 'VARCHAR(500)', nullable: false },
      { name: 'english_description', type: 'TEXT', nullable: false },
      { name: 'selling_point', type: 'JSON', nullable: true, comment: '英文5卖点' },
      { name: 'image1', type: 'VARCHAR(2083)', nullable: false },
      { name: 'image2', type: 'VARCHAR(2083)', nullable: true },
      { name: 'image3', type: 'VARCHAR(2083)', nullable: true },
      { name: 'image4', type: 'VARCHAR(2083)', nullable: true },
      { name: 'image5', type: 'VARCHAR(2083)', nullable: true },
      { name: 'cost_price', type: 'DECIMAL(10,2)', nullable: true },
      { name: 'package_weight', type: 'INT', nullable: true, comment: '包装重量(克)' },
      { name: 'package_length', type: 'DECIMAL(8,2)', nullable: true, comment: '包装长度(cm)' },
      { name: 'package_width', type: 'DECIMAL(8,2)', nullable: true, comment: '包装宽度(cm)' },
      { name: 'package_height', type: 'DECIMAL(8,2)', nullable: true, comment: '包装高度(cm)' },
      { name: 'purchase_link', type: 'VARCHAR(1000)', nullable: true },
      { name: 'claim_time', type: 'TIMESTAMP', nullable: true, comment: '认领时间' },
      { name: 'claim_platform', type: 'VARCHAR(50)', nullable: true, comment: '认领平台' },
      { name: 'listing_count', type: 'INT', default: '0', comment: '刊登次数' },
      { name: 'remarks', type: 'TEXT', nullable: true },
      { name: 'status', type: "ENUM('draft', 'active', 'inactive')", default: "'draft'" },
      { name: 'created_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP' },
      { name: 'updated_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP' }
    ],
    indexes: [
      { name: 'idx_products_sku', columns: ['sku'] },
      { name: 'idx_products_ean', columns: ['ean'] },
      { name: 'idx_products_category', columns: ['category_id'] },
      { name: 'idx_products_source', columns: ['source'] },
      { name: 'idx_products_status', columns: ['status'] }
    ],
    foreignKeys: [
      {
        name: 'fk_products_category',
        column: 'category_id',
        referencedTable: 'product_categories',
        referencedColumn: 'id',
        onDelete: 'SET NULL'
      }
    ]
  },

  // 平台配置表
  {
    tableName: 'stores_platforms',
    columns: [
      { name: 'id', type: 'INT', autoIncrement: true, primaryKey: true },
      { name: 'platform_code', type: 'VARCHAR(50)', nullable: false, unique: true, comment: '平台代码，如amazon、ebay、phh' },
      { name: 'platform_name', type: 'VARCHAR(100)', nullable: false, comment: '平台显示名称' },
      { name: 'platform_name_en', type: 'VARCHAR(100)', nullable: false, comment: '平台英文名称' },
      { name: 'logo_url', type: 'VARCHAR(500)', nullable: true, comment: '平台Logo图片链接' },
      { name: 'status', type: "ENUM('active', 'inactive')", default: "'active'", comment: '平台状态' },
      { name: 'sort_order', type: 'INT', default: '0', comment: '排序顺序' },
      { name: 'config_fields', type: 'JSON', nullable: true, comment: '平台特定配置字段定义' },
      { name: 'required_languages', type: 'JSON', nullable: true, comment: '该平台需要的语言列表，如["LT","LV","EE","FI"]' },
      { name: 'description', type: 'TEXT', nullable: true, comment: '平台描述' },
      { name: 'created_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP' },
      { name: 'updated_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP' }
    ],
    indexes: [
      { name: 'idx_platforms_code', columns: ['platform_code'], unique: true },
      { name: 'idx_platforms_status', columns: ['status'] },
      { name: 'idx_platforms_sort', columns: ['sort_order'] }
    ]
  },

  // 统一店铺表
  {
    tableName: 'stores_updata',
    columns: [
      { name: 'id', type: 'BIGINT', autoIncrement: true, primaryKey: true, comment: '全局ID，从100开始' },
      { name: 'platform_code', type: 'VARCHAR(50)', nullable: false, comment: '平台代码，关联platforms表' },
      { name: 'store_name', type: 'VARCHAR(200)', nullable: false, comment: '店铺名称，可修改' },
      { name: 'status', type: "ENUM('active', 'failed')", default: "'active'", comment: '绑定状态：活跃-正常，失败-绑定失败' },
      { name: 'api_key', type: 'VARCHAR(500)', nullable: true, comment: 'API密钥' },
      { name: 'token', type: 'VARCHAR(500)', nullable: true, comment: '短期token，30天有效' },
      { name: 'token_expires_at', type: 'TIMESTAMP', nullable: true, comment: 'token过期时间' },
      { name: 'site', type: 'VARCHAR(50)', nullable: true, comment: '平台销售站点' },
      { name: 'platform_config', type: 'JSON', nullable: true, comment: '平台特定配置信息' },
      { name: 'created_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP', comment: '创建时间（北京时间）' },
      { name: 'updated_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', comment: '修改时间（北京时间）' }
    ],
    indexes: [
      { name: 'idx_stores_platform', columns: ['platform_code'] },
      { name: 'idx_stores_status', columns: ['status'] },
      { name: 'idx_stores_name', columns: ['store_name'] }
    ],
    foreignKeys: [
      {
        name: 'fk_stores_platform',
        column: 'platform_code',
        referencedTable: 'stores_platforms',
        referencedColumn: 'platform_code',
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      }
    ]
  },



  // 订单表
  {
    tableName: 'orders_updata',
    columns: [
      { name: 'id', type: 'BIGINT', autoIncrement: true, primaryKey: true, comment: '全局ID，从10001开始' },
      { name: 'order_number', type: 'VARCHAR(100)', nullable: false, unique: true, comment: '订单编号，唯一不可修改' },
      { name: 'transaction_id', type: 'VARCHAR(100)', nullable: false, unique: true, comment: '交易编号，唯一不可修改' },
      { name: 'store_name', type: 'VARCHAR(200)', nullable: false, comment: '店铺名（店铺别名）' },
      { name: 'platform', type: 'VARCHAR(50)', nullable: false, comment: '所属平台' },
      { name: 'payment_time', type: 'TIMESTAMP', nullable: true, comment: '付款时间（北京时间）' },
      { name: 'platform_order_status', type: 'VARCHAR(50)', nullable: true, comment: '平台订单状态' },
      { name: 'order_remarks', type: 'TEXT', nullable: true, comment: '订单备注' },
      { name: 'ioss_number', type: 'VARCHAR(50)', nullable: true, comment: 'IOSS编号' },

      // 客户信息
      { name: 'customer_id', type: 'VARCHAR(100)', nullable: false, comment: '客户ID，唯一不可修改' },
      { name: 'customer_name', type: 'VARCHAR(200)', nullable: false, comment: '客户姓名' },
      { name: 'phone1', type: 'VARCHAR(50)', nullable: true, comment: '电话1' },
      { name: 'phone2', type: 'VARCHAR(50)', nullable: true, comment: '电话2' },
      { name: 'country', type: 'VARCHAR(100)', nullable: true, comment: '国家（中文）' },
      { name: 'country_en', type: 'VARCHAR(100)', nullable: true, comment: '国家（英文）' },
      { name: 'state_province', type: 'VARCHAR(100)', nullable: true, comment: '所属地区（省/州）' },
      { name: 'city', type: 'VARCHAR(100)', nullable: true, comment: '所属城市' },
      { name: 'postal_code', type: 'VARCHAR(20)', nullable: true, comment: '邮政编码' },
      { name: 'shipping_address', type: 'TEXT', nullable: true, comment: '包裹邮寄地址' },
      { name: 'email', type: 'VARCHAR(200)', nullable: true, comment: '联系邮箱' },

      // 商品信息
      { name: 'product_name_en', type: 'VARCHAR(500)', nullable: true, comment: '商品英文名称' },
      { name: 'product_sku', type: 'VARCHAR(100)', nullable: true, comment: '商品SKU' },
      { name: 'product_quantity', type: 'INT', nullable: true, comment: '商品SKU数量' },
      { name: 'product_unit_price', type: 'DECIMAL(10,2)', nullable: true, comment: '商品销售单价' },
      { name: 'product_total_price', type: 'DECIMAL(10,2)', nullable: true, comment: '商品销售总价' },
      { name: 'shipping_fee', type: 'DECIMAL(10,2)', nullable: true, comment: '运费收入' },
      { name: 'product_image_url', type: 'VARCHAR(500)', nullable: true, comment: '商品图片链接' },
      { name: 'currency', type: 'VARCHAR(10)', nullable: true, comment: '货币币种' },


      { name: 'created_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP', comment: '创建时间（北京时间）' },
      { name: 'updated_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', comment: '更新时间' }
    ],
    indexes: [
      { name: 'idx_orders_order_number', columns: ['order_number'], unique: true },
      { name: 'idx_orders_transaction_id', columns: ['transaction_id'], unique: true },
      { name: 'idx_orders_platform', columns: ['platform'] },
      { name: 'idx_orders_customer_id', columns: ['customer_id'] },
      { name: 'idx_orders_store_name', columns: ['store_name'] },
      { name: 'idx_orders_payment_time', columns: ['payment_time'] },
      { name: 'idx_orders_created_at', columns: ['created_at'] }
    ]
  },

  // 产品上架主表
  {
    tableName: 'uploadproduct_listings',
    columns: [
      { name: 'id', type: 'INT', autoIncrement: true, primaryKey: true },
      // 关联字段
      { name: 'dropship_product_id', type: 'BIGINT', nullable: false, comment: '关联铺货产品表(product_dropship)' },
      { name: 'platform_code', type: 'VARCHAR(50)', nullable: false, comment: '平台代码(amazon/ebay/shopify等)' },
      { name: 'store_id', type: 'BIGINT', nullable: false, comment: '关联店铺表(stores_updata)' },
      { name: 'platform_category_id', type: 'VARCHAR(100)', nullable: true, comment: '平台类目ID' },
      // 冗余关键基础信息
      { name: 'sku', type: 'VARCHAR(100)', nullable: false, comment: '产品SKU（关键标识，冗余存储）' },
      { name: 'ean', type: 'VARCHAR(50)', nullable: true, comment: 'EAN码（重要标识，冗余存储）' },
      { name: 'english_title', type: 'VARCHAR(500)', nullable: true, comment: '英文标题（显示用，冗余存储）' },
      { name: 'image1', type: 'VARCHAR(500)', nullable: true, comment: '图片1' },

      // 上架平台特定信息
      { name: 'upstores_sku', type: 'VARCHAR(100)', nullable: true, comment: '允许修改上架平台SKU' },
      { name: 'upstores_ean', type: 'VARCHAR(50)', nullable: true, comment: '允许修改上架平台EAN' },
      // 多语言内容
      { name: 'multi_titles', type: 'JSON', nullable: true, comment: '多语言标题，结构与平台语言配置对应' },
      { name: 'multi_descriptions', type: 'JSON', nullable: true, comment: '多语言描述，结构与平台语言配置对应' },
      { name: 'multi_selling_point', type: 'JSON', nullable: true, comment: '多语言五点卖点，结构与平台语言配置对应' },
      { name: 'listings_translation_status', type: "ENUM('pending', 'completed')", default: "'pending'", comment: '多语言翻译状态' },
      // 平台特定参数
      { name: 'platform_data', type: 'JSON', nullable: true, comment: '平台特定的产品参数，上架产品需要到' },
      { name: 'platform_attributes', type: 'JSON', nullable: true, comment: '平台特定的类目参数和值' },
      { name: 'attributes_status', type: "ENUM('current', 'outdated', 'needs_review')", default: "'current'", comment: '参数状态' },
      // 销售参数
      { name: 'discounted_price', type: 'DECIMAL(10,2)', nullable: true, comment: '折扣价格（实际售价）' },
      { name: 'original_price', type: 'DECIMAL(10,2)', nullable: true, comment: '原价（划线价）' },
      { name: 'discount_end_date', type: 'TIMESTAMP', nullable: true, comment: '折扣结束日期' },
      { name: 'discount_start_date', type: 'TIMESTAMP', nullable: true, comment: '折扣开始日期' },
      { name: 'stock_quantity', type: 'INT', default: '50', comment: '库存数量' },
      { name: 'discount', type: 'DECIMAL(5,2)', nullable: true, comment: '折扣率(如0.1表示10%折扣)' },
      { name: 'currency', type: 'VARCHAR(10)', default: "'EUR'", comment: '货币单位' },
      // 状态管理
      { name: 'status', type: "ENUM('draft', 'pending', 'active', 'failed', 'inactive')", default: "'draft'", comment: '上架状态' },
      { name: 'listing_id', type: 'VARCHAR(100)', nullable: true, comment: '平台返回的listing ID' },
      // 错误和同步信息
      { name: 'error_message', type: 'TEXT', nullable: true, comment: '上架失败的错误信息' },
      { name: 'last_sync_at', type: 'TIMESTAMP', nullable: true, comment: '最后同步时间' },
      // 时间戳
      { name: 'created_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP' },
      { name: 'uplisting_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP' }
    ],
    indexes: [
      { name: 'unique_product_platform', columns: ['dropship_product_id', 'platform_code', 'store_id'], unique: true },
      { name: 'unique_sku_platform', columns: ['sku', 'platform_code', 'store_id'], unique: true },
      { name: 'idx_platform_status', columns: ['platform_code', 'status'] },
      { name: 'idx_store_status', columns: ['store_id', 'status'] },
      { name: 'idx_sku', columns: ['sku'] },
      { name: 'idx_listing_id', columns: ['listing_id'] },
      { name: 'idx_attributes_status', columns: ['attributes_status'] },
      { name: 'idx_category_attributes', columns: ['platform_code', 'platform_category_id', 'attributes_status'] },
      { name: 'idx_translation_status', columns: ['listings_translation_status'] }
    ],
    foreignKeys: [
      {
        name: 'fk_uploadproduct_listings_dropship_product',
        column: 'dropship_product_id',
        referencedTable: 'product_dropship',
        referencedColumn: 'id',
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      {
        name: 'fk_uploadproduct_listings_store',
        column: 'store_id',
        referencedTable: 'stores_updata',
        referencedColumn: 'id',
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      }
    ]
  },

  // 平台类目表
  {
    tableName: 'uploadproduct_categories',
    columns: [
      { name: 'id', type: 'INT', autoIncrement: true, primaryKey: true },
      // 平台和类目信息
      { name: 'platform_code', type: 'VARCHAR(50)', nullable: false, comment: '平台代码' },
      { name: 'category_id', type: 'VARCHAR(100)', nullable: false, comment: '平台类目ID' },
      { name: 'parent_category_id', type: 'VARCHAR(100)', nullable: true, comment: '父类目ID' },
      // 类目名称和路径
      { name: 'category_name', type: 'VARCHAR(200)', nullable: true, comment: '平台原始类目名称（英语）' },
      { name: 'category_name_cn', type: 'VARCHAR(200)', nullable: true, comment: '中文类目名称(便于前端显示)' },
      { name: 'category_path', type: 'VARCHAR(500)', nullable: true, comment: '完整类目路径(用/分隔，从根到当前类目)' },
      { name: 'level', type: 'INT', default: '1', comment: '类目层级' },
      { name: 'is_leaf', type: 'BOOLEAN', default: 'FALSE', comment: '是否叶子类目(只有叶子类目可上架产品)' },
      // 类目状态
      { name: 'status', type: "ENUM('active', 'inactive')", default: "'active'" },
      // 时间戳
      { name: 'created_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP' },
      { name: 'updated_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP' }
    ],
    indexes: [
      { name: 'unique_platform_category', columns: ['platform_code', 'category_id'], unique: true },
      { name: 'idx_platform_parent', columns: ['platform_code', 'parent_category_id'] },
      { name: 'idx_platform_level', columns: ['platform_code', 'level'] },
      { name: 'idx_leaf_categories', columns: ['platform_code', 'is_leaf'] }
    ]
  },

  // 平台类目参数表
  {
    tableName: 'uploadproduct_category_attributes',
    columns: [
      { name: 'id', type: 'INT', autoIncrement: true, primaryKey: true },
      // 关联信息
      { name: 'platform_code', type: 'VARCHAR(50)', nullable: false, comment: '平台代码' },
      { name: 'category_id', type: 'VARCHAR(100)', nullable: false, comment: '平台类目ID' },
      // 参数基本信息
      { name: 'attribute_name', type: 'VARCHAR(100)', nullable: false, comment: '参数英文名称(平台原始)' },
      { name: 'attribute_name_cn', type: 'VARCHAR(200)', nullable: true, comment: '参数中文名称(翻译后)' },
      { name: 'attribute_label', type: 'JSON', nullable: true, comment: '多语言参数标签(平台提供)' },
      { name: 'attribute_type', type: "ENUM('text', 'number', 'select', 'multiselect', 'boolean', 'date')", nullable: false },
      // 参数约束
      { name: 'is_required', type: 'BOOLEAN', default: 'FALSE', comment: '是否必填' },
      { name: 'is_variation', type: 'BOOLEAN', default: 'FALSE', comment: '是否变体参数' },
      { name: 'max_length', type: 'INT', nullable: true, comment: '最大长度(文本类型)' },
      { name: 'min_value', type: 'DECIMAL(10,2)', nullable: true, comment: '最小值(数字类型)' },
      { name: 'max_value', type: 'DECIMAL(10,2)', nullable: true, comment: '最大值(数字类型)' },
      // 选项值处理
      { name: 'options', type: 'JSON', nullable: true, comment: '平台提供的原始选项值' },
      { name: 'options_cn', type: 'JSON', nullable: true, comment: '翻译后的中文选项值' },
      { name: 'has_predefined_options', type: 'BOOLEAN', default: 'FALSE', comment: '是否有预定义选项(区分下拉和输入)' },
      // 显示和分组
      { name: 'sort_order', type: 'INT', default: '0', comment: '显示顺序' },
      { name: 'group_name', type: 'VARCHAR(100)', nullable: true, comment: '参数分组' },
      { name: 'group_name_cn', type: 'VARCHAR(200)', nullable: true, comment: '参数分组中文名' },
      // 翻译状态
      { name: 'translation_status', type: "ENUM('pending', 'completed', 'failed')", default: "'pending'" },
      // 时间戳
      { name: 'created_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP' },
      { name: 'updated_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP' }
    ],
    indexes: [
      { name: 'unique_category_attribute', columns: ['platform_code', 'category_id', 'attribute_name'], unique: true },
      { name: 'idx_platform_category', columns: ['platform_code', 'category_id'] },
      { name: 'idx_required_attributes', columns: ['platform_code', 'category_id', 'is_required'] },
      { name: 'idx_translation_status', columns: ['translation_status'] }
    ]
  },

  // 系统设置表
  {
    tableName: 'system_settings',
    columns: [
      { name: 'id', type: 'INT', autoIncrement: true, primaryKey: true },
      // 设置标识
      { name: 'setting_key', type: 'VARCHAR(100)', nullable: false, unique: true, comment: '设置键名（唯一标识）' },
      { name: 'setting_value', type: 'JSON', nullable: true, comment: '设置值（JSON格式存储复杂配置）' },
      { name: 'category', type: 'VARCHAR(50)', nullable: false, comment: '设置分类（translation/system/business等）' },
      // 显示信息
      { name: 'display_name', type: 'VARCHAR(200)', nullable: false, comment: '显示名称' },
      { name: 'description', type: 'TEXT', nullable: true, comment: '设置描述' },
      // 权限控制
      { name: 'is_public', type: 'BOOLEAN', default: 'FALSE', comment: '是否前端可见' },
      { name: 'is_editable', type: 'BOOLEAN', default: 'TRUE', comment: '是否可编辑' },
      // 验证和默认值
      { name: 'validation_rules', type: 'JSON', nullable: true, comment: '验证规则（JSON格式）' },
      { name: 'default_value', type: 'JSON', nullable: true, comment: '默认值（JSON格式）' },
      // 排序
      { name: 'sort_order', type: 'INT', default: '0', comment: '排序' },
      // 时间戳
      { name: 'created_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP' },
      { name: 'updated_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP' }
    ],
    indexes: [
      { name: 'idx_setting_key', columns: ['setting_key'], unique: true },
      { name: 'idx_category', columns: ['category'] },
      { name: 'idx_public_settings', columns: ['is_public'] },
      { name: 'idx_category_order', columns: ['category', 'sort_order'] }
    ]
  }
];

export class MigrationManager {
  /**
   * 运行所有迁移
   */
  public async runMigrations(): Promise<void> {
    try {
      logger.info('Starting database migrations...');

      for (const schema of tableSchemas) {
        await this.createOrUpdateTable(schema);
      }

      // 初始化平台数据
      await this.initializePlatformData();

      // 插入测试数据
      await seedDataManager.seedAll();

      logger.info('Database migrations completed successfully');
    } catch (error) {
      logger.error('Database migration failed:', error);
      throw error;
    }
  }

  /**
   * 创建或更新数据表
   */
  private async createOrUpdateTable(schema: TableSchema): Promise<void> {
    const tableExists = await this.tableExists(schema.tableName);

    if (!tableExists) {
      await this.createTable(schema);
    } else {
      await this.updateTable(schema);
    }
  }

  /**
   * 检查表是否存在
   */
  private async tableExists(tableName: string): Promise<boolean> {
    const result = await database.queryOne<{ count: number }>(
      `SELECT COUNT(*) as count FROM information_schema.tables 
       WHERE table_schema = DATABASE() AND table_name = ?`,
      [tableName]
    );
    return (result?.count || 0) > 0;
  }

  /**
   * 创建新表
   */
  private async createTable(schema: TableSchema): Promise<void> {
    logger.info(`Creating table: ${schema.tableName}`);

    const columnDefinitions = schema.columns.map(col => this.buildColumnDefinition(col));
    
    let sql = `CREATE TABLE ${schema.tableName} (\n`;
    sql += columnDefinitions.join(',\n');
    
    // 添加主键
    const primaryKeys = schema.columns.filter(col => col.primaryKey).map(col => col.name);
    if (primaryKeys.length > 0) {
      sql += `,\n  PRIMARY KEY (${primaryKeys.join(', ')})`;
    }

    sql += '\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci';

    // 设置自增起始值
    if (schema.tableName === 'orders_updata') {
      sql += ' AUTO_INCREMENT=10001';
    } else if (schema.tableName === 'stores_updata') {
      sql += ' AUTO_INCREMENT=100';
    } else if (schema.tableName === 'product_dropship') {
      sql += ' AUTO_INCREMENT=1000001';
    } else if (schema.tableName === 'uploadproduct_listings') {
      sql += ' AUTO_INCREMENT=1000001';
      
    }

    await database.execute(sql);

    // 创建索引
    if (schema.indexes) {
      for (const index of schema.indexes) {
        await this.createIndex(schema.tableName, index);
      }
    }

    // 创建外键
    if (schema.foreignKeys) {
      for (const fk of schema.foreignKeys) {
        await this.createForeignKey(schema.tableName, fk);
      }
    }

    logger.info(`Table ${schema.tableName} created successfully`);
  }

  /**
   * 更新现有表
   */
  private async updateTable(schema: TableSchema): Promise<void> {
    logger.info(`Checking table structure: ${schema.tableName}`);

    const existingColumns = await this.getTableColumns(schema.tableName);
    const existingColumnNames = existingColumns.map(col => col.COLUMN_NAME);

    // 检查需要添加的新字段
    for (const column of schema.columns) {
      if (!existingColumnNames.includes(column.name)) {
        await this.addColumn(schema.tableName, column);
      }
    }

    logger.info(`Table ${schema.tableName} structure updated`);
  }

  /**
   * 获取表的现有字段
   */
  private async getTableColumns(tableName: string): Promise<any[]> {
    return await database.query(
      `SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, EXTRA
       FROM information_schema.columns 
       WHERE table_schema = DATABASE() AND table_name = ?
       ORDER BY ORDINAL_POSITION`,
      [tableName]
    );
  }

  /**
   * 添加新字段
   */
  private async addColumn(tableName: string, column: ColumnDefinition): Promise<void> {
    logger.info(`Adding column ${column.name} to table ${tableName}`);

    const columnDef = this.buildColumnDefinition(column);
    const sql = `ALTER TABLE ${tableName} ADD COLUMN ${columnDef}`;
    
    await database.execute(sql);
    logger.info(`Column ${column.name} added to table ${tableName}`);
  }

  /**
   * 构建字段定义SQL
   */
  private buildColumnDefinition(column: ColumnDefinition): string {
    let def = `  ${column.name} ${column.type}`;
    
    if (!column.nullable) {
      def += ' NOT NULL';
    }
    
    if (column.default !== undefined) {
      def += ` DEFAULT ${column.default}`;
    }
    
    if (column.autoIncrement) {
      def += ' AUTO_INCREMENT';
    }
    
    if (column.unique && !column.primaryKey) {
      def += ' UNIQUE';
    }
    
    if (column.comment) {
      def += ` COMMENT '${column.comment}'`;
    }
    
    return def;
  }

  /**
   * 创建索引
   */
  private async createIndex(tableName: string, index: IndexDefinition): Promise<void> {
    const indexType = index.unique ? 'UNIQUE INDEX' : 'INDEX';
    const sql = `CREATE ${indexType} ${index.name} ON ${tableName} (${index.columns.join(', ')})`;
    
    try {
      await database.execute(sql);
      logger.info(`Index ${index.name} created on table ${tableName}`);
    } catch (error) {
      // 索引可能已存在，忽略错误
      logger.warn(`Index ${index.name} might already exist on table ${tableName}`);
    }
  }

  /**
   * 创建外键
   */
  private async createForeignKey(tableName: string, fk: ForeignKeyDefinition): Promise<void> {
    let sql = `ALTER TABLE ${tableName} ADD CONSTRAINT ${fk.name} 
               FOREIGN KEY (${fk.column}) REFERENCES ${fk.referencedTable}(${fk.referencedColumn})`;
    
    if (fk.onDelete) {
      sql += ` ON DELETE ${fk.onDelete}`;
    }
    
    if (fk.onUpdate) {
      sql += ` ON UPDATE ${fk.onUpdate}`;
    }
    
    try {
      await database.execute(sql);
      logger.info(`Foreign key ${fk.name} created on table ${tableName}`);
    } catch (error) {
      // 外键可能已存在，忽略错误
      logger.warn(`Foreign key ${fk.name} might already exist on table ${tableName}`);
    }
  }

  /**
   * 初始化平台数据
   */
  private async initializePlatformData(): Promise<void> {
    try {
      // 检查是否已有平台数据
      const existingPlatforms = await database.query('SELECT COUNT(*) as count FROM stores_platforms');
      if ((existingPlatforms[0] as any).count > 0) {
        logger.info('Platform data already exists, skipping initialization');
        return;
      }

      // 插入初始平台数据
      const platforms = [
        {
          platform_code: 'worten',
          platform_name: 'worten',
          platform_name_en: 'worten',
          logo_url: '/logos/worten.png',
          status: 'active',
          sort_order: 2,
          config_fields: JSON.stringify({
            required_fields: ['app_id', 'dev_id', 'cert_id', 'user_token'],
            optional_fields: ['site', 'store_subscription']
          }),
          required_languages: JSON.stringify(['PT', 'ES',]),
          description: '全球知名的在线拍卖及购物网站'
        },
        {
          platform_code: 'phh',
          platform_name: 'PHH平台',
          platform_name_en: 'PHH Platform',
          logo_url: '/logos/phh.png',
          status: 'active',
          sort_order: 3,
          config_fields: JSON.stringify({
            required_fields: ['email', 'password'],
            optional_fields: ['shop_id']
          }),
          required_languages: JSON.stringify(['LT', 'LV', 'EE', 'FI']),
          description: 'PHH电商平台'
        },

      ];

      for (const platform of platforms) {
        await database.execute(`
          INSERT INTO stores_platforms (
            platform_code, platform_name, platform_name_en, logo_url,
            status, sort_order, config_fields, required_languages, description
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          platform.platform_code,
          platform.platform_name,
          platform.platform_name_en,
          platform.logo_url,
          platform.status,
          platform.sort_order,
          platform.config_fields,
          platform.required_languages,
          platform.description
        ]);
      }

      logger.info('Platform data initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize platform data:', error);
      throw error;
    }
  }
}

export const migrationManager = new MigrationManager();
