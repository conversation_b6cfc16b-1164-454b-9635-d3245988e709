"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/catalog/page",{

/***/ "(app-pages-browser)/./src/components/products/product-catalog-page.tsx":
/*!**********************************************************!*\
  !*** ./src/components/products/product-catalog-page.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductCatalogPage: function() { return /* binding */ ProductCatalogPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/expand.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/hooks/useCategories */ \"(app-pages-browser)/./src/hooks/useCategories.ts\");\n/* __next_internal_client_entry_do_not_use__ ProductCatalogPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductCatalogPage() {\n    _s();\n    const [expandedNodes, setExpandedNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"1\",\n        \"2\"\n    ]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedNodeLevel, setSelectedNodeLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 存储选中节点的层级\n    ;\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdding, setIsAdding] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_11__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    // 使用API hooks\n    const { categoryTree: apiCategoryTree, loading: categoriesLoading, error: categoriesError, refetch: refetchCategories } = (0,_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategoryTree)();\n    const { createCategory, updateCategory, deleteCategory, loading: operationLoading } = (0,_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategories)();\n    // 转换API数据为显示格式\n    const convertCategoryToNode = (category)=>({\n            id: category.id.toString(),\n            name: category.chinese_name,\n            englishName: category.english_name,\n            categoryCode: category.category_code || \"\",\n            description: category.category_description || \"\",\n            tags: category.attribute_tags || [],\n            autoSku: category.auto_sku === \"enabled\",\n            createdAt: new Date(category.created_at).toLocaleString(\"zh-CN\"),\n            status: category.status === \"enabled\" ? \"active\" : \"inactive\",\n            children: category.children ? category.children.map(convertCategoryToNode) : undefined\n        });\n    const catalogTree = apiCategoryTree ? apiCategoryTree.map(convertCategoryToNode) : [];\n    const toggleExpanded = (nodeId)=>{\n        setExpandedNodes((prev)=>prev.includes(nodeId) ? prev.filter((id)=>id !== nodeId) : [\n                ...prev,\n                nodeId\n            ]);\n    };\n    // 递归获取所有有子节点的节点ID\n    const getAllExpandableNodeIds = (nodes)=>{\n        const ids = [];\n        const traverse = (nodeList)=>{\n            nodeList.forEach((node)=>{\n                if (node.children && node.children.length > 0) {\n                    ids.push(node.id);\n                    traverse(node.children);\n                }\n            });\n        };\n        traverse(nodes);\n        return ids;\n    };\n    // 展开全部\n    const handleExpandAll = ()=>{\n        const allExpandableIds = getAllExpandableNodeIds(catalogTree);\n        setExpandedNodes(allExpandableIds);\n    };\n    // 折叠全部\n    const handleCollapseAll = ()=>{\n        setExpandedNodes([]);\n    };\n    // 递归搜索匹配的节点\n    const searchNodes = (nodes, searchTerm)=>{\n        if (!searchTerm.trim()) return nodes;\n        const results = [];\n        const lowerSearchTerm = searchTerm.toLowerCase();\n        const searchInNode = (node)=>{\n            var _node_categoryCode;\n            // 检查当前节点是否匹配\n            const nameMatch = node.name.toLowerCase().includes(lowerSearchTerm);\n            const englishNameMatch = node.englishName.toLowerCase().includes(lowerSearchTerm);\n            const codeMatch = (_node_categoryCode = node.categoryCode) === null || _node_categoryCode === void 0 ? void 0 : _node_categoryCode.toLowerCase().includes(lowerSearchTerm);\n            const isCurrentMatch = nameMatch || englishNameMatch || codeMatch;\n            // 递归搜索子节点\n            const matchingChildren = [];\n            if (node.children) {\n                node.children.forEach((child)=>{\n                    const childResult = searchInNode(child);\n                    if (childResult) {\n                        matchingChildren.push(childResult);\n                    }\n                });\n            }\n            // 如果当前节点匹配或有匹配的子节点，则返回节点\n            if (isCurrentMatch || matchingChildren.length > 0) {\n                return {\n                    ...node,\n                    children: matchingChildren.length > 0 ? matchingChildren : node.children\n                };\n            }\n            return null;\n        };\n        nodes.forEach((node)=>{\n            const result = searchInNode(node);\n            if (result) {\n                results.push(result);\n            }\n        });\n        return results;\n    };\n    // 获取搜索结果中所有匹配节点的路径（用于自动展开）\n    const getMatchingNodePaths = (nodes, searchTerm)=>{\n        if (!searchTerm.trim()) return [];\n        const paths = [];\n        const lowerSearchTerm = searchTerm.toLowerCase();\n        const findPaths = function(nodeList) {\n            let currentPath = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n            nodeList.forEach((node)=>{\n                var _node_categoryCode;\n                const newPath = [\n                    ...currentPath,\n                    node.id\n                ];\n                // 检查当前节点是否匹配\n                const nameMatch = node.name.toLowerCase().includes(lowerSearchTerm);\n                const englishNameMatch = node.englishName.toLowerCase().includes(lowerSearchTerm);\n                const codeMatch = (_node_categoryCode = node.categoryCode) === null || _node_categoryCode === void 0 ? void 0 : _node_categoryCode.toLowerCase().includes(lowerSearchTerm);\n                if (nameMatch || englishNameMatch || codeMatch) {\n                    // 添加到匹配节点的所有父节点路径\n                    newPath.slice(0, -1).forEach((parentId)=>{\n                        if (!paths.includes(parentId)) {\n                            paths.push(parentId);\n                        }\n                    });\n                }\n                // 递归搜索子节点\n                if (node.children && node.children.length > 0) {\n                    findPaths(node.children, newPath);\n                }\n            });\n        };\n        findPaths(nodes);\n        return paths;\n    };\n    // 处理搜索\n    const handleSearch = ()=>{\n        if (!searchValue.trim()) {\n            // 如果搜索框为空，重置展开状态\n            setExpandedNodes([\n                \"1\",\n                \"2\"\n            ]) // 恢复默认展开状态\n            ;\n            return;\n        }\n        // 获取匹配节点的父节点路径并自动展开\n        const pathsToExpand = getMatchingNodePaths(catalogTree, searchValue);\n        setExpandedNodes(pathsToExpand);\n    };\n    // 重置搜索\n    const handleResetSearch = ()=>{\n        setSearchValue(\"\");\n    };\n    // 过滤后的目录树（用于显示搜索结果）\n    const filteredCatalogTree = searchValue.trim() ? searchNodes(catalogTree, searchValue) : catalogTree;\n    const handleAdd = ()=>{\n        setIsAdding(true);\n        setIsEditing(false);\n        setSelectedNode(null);\n        setSelectedNodeLevel(0) // 一级分类的层级为0\n        ;\n        setShowEditDialog(true);\n        setFormData({\n            name: \"\",\n            englishName: \"\",\n            categoryCode: \"\",\n            description: \"\",\n            tags: [],\n            autoSku: false,\n            status: \"active\"\n        });\n    };\n    const handleEditNode = (node)=>{\n        setSelectedNode(node);\n        setIsEditing(true);\n        setIsAdding(false);\n        setShowEditDialog(true);\n        setFormData({\n            name: node.name,\n            englishName: node.englishName,\n            categoryCode: node.categoryCode,\n            description: node.description,\n            tags: node.tags,\n            autoSku: node.autoSku,\n            status: node.status\n        });\n    };\n    const handleDeleteNode = async (node)=>{\n        const confirmed = await confirm({\n            title: \"删除分类\",\n            description: '确定要删除分类\"'.concat(node.name, '\"吗？此操作不可撤销。'),\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (confirmed) {\n            try {\n                await deleteCategory(parseInt(node.id));\n                refetchCategories();\n                // 显示删除成功提示\n                toast({\n                    title: \"删除成功\"\n                });\n            } catch (error) {\n                console.error(\"删除失败:\", error);\n                // 显示删除失败提示\n                toast({\n                    title: \"删除失败\",\n                    description: error instanceof Error ? error.message : \"删除时发生未知错误\",\n                    variant: \"destructive\"\n                });\n            }\n        }\n    };\n    const handleAddChild = (parentNode, parentLevel)=>{\n        setIsAdding(true);\n        setIsEditing(false);\n        setSelectedNode(parentNode);\n        setSelectedNodeLevel(parentLevel) // 存储父节点的层级\n        ;\n        setShowEditDialog(true);\n        setFormData({\n            name: \"\",\n            englishName: \"\",\n            categoryCode: \"\",\n            description: \"\",\n            tags: [],\n            autoSku: false,\n            status: \"active\"\n        });\n    };\n    const handleSave = async ()=>{\n        try {\n            var _formData_name, _formData_englishName;\n            // 验证必填字段\n            if (!((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim())) {\n                toast({\n                    title: \"验证失败\",\n                    description: \"请输入中文名称\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (!((_formData_englishName = formData.englishName) === null || _formData_englishName === void 0 ? void 0 : _formData_englishName.trim())) {\n                toast({\n                    title: \"验证失败\",\n                    description: \"请输入英文名称\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (isAdding) {\n                var _formData_categoryCode, _formData_description;\n                // 创建新分类\n                const categoryCode = (_formData_categoryCode = formData.categoryCode) === null || _formData_categoryCode === void 0 ? void 0 : _formData_categoryCode.trim();\n                // 根据是否有选中的父节点来确定层级和父ID\n                let categoryLevel = 1;\n                let parentId = null;\n                if (selectedNode) {\n                    // 如果有选中的父节点，说明是添加子分类\n                    parentId = parseInt(selectedNode.id);\n                    // 根据父节点的层级确定子节点层级\n                    // selectedNodeLevel: 0=一级, 1=二级, 2=三级, 3=四级, 4=五级, 5=六级 (前端显示层级)\n                    // category_level: 1=一级, 2=二级, 3=三级, 4=四级, 5=五级, 6=六级, 7=七级 (后端数据库层级)\n                    // 子分类的层级 = 父分类的数据库层级 + 1 = (selectedNodeLevel + 1) + 1\n                    categoryLevel = selectedNodeLevel + 2;\n                }\n                const createData = {\n                    // 分类编码处理：如果有值则转换为大写，如果为空则不传递该字段\n                    ...categoryCode ? {\n                        category_code: categoryCode.toUpperCase()\n                    } : {},\n                    chinese_name: formData.name.trim(),\n                    english_name: formData.englishName.trim(),\n                    status: formData.status === \"active\" ? \"enabled\" : \"disabled\",\n                    auto_sku: formData.autoSku ? \"enabled\" : \"disabled\",\n                    category_level: categoryLevel,\n                    parent_id: parentId,\n                    category_description: ((_formData_description = formData.description) === null || _formData_description === void 0 ? void 0 : _formData_description.trim()) || \"\",\n                    attribute_tags: formData.tags || [],\n                    sort_order: 0\n                };\n                await createCategory(createData);\n                setIsAdding(false);\n                setShowEditDialog(false);\n                setFormData({}) // 清空表单数据\n                ;\n                await refetchCategories() // 等待刷新完成\n                ;\n                // 显示成功提示\n                toast({\n                    title: \"创建成功\"\n                });\n            } else if (selectedNode && isEditing) {\n                var _formData_description1;\n                // 更新分类 - 编辑时不允许修改分类编码\n                const updateData = {\n                    chinese_name: formData.name.trim(),\n                    english_name: formData.englishName.trim(),\n                    status: formData.status === \"active\" ? \"enabled\" : \"disabled\",\n                    auto_sku: formData.autoSku ? \"enabled\" : \"disabled\",\n                    category_description: ((_formData_description1 = formData.description) === null || _formData_description1 === void 0 ? void 0 : _formData_description1.trim()) || \"\",\n                    attribute_tags: formData.tags || []\n                };\n                await updateCategory(parseInt(selectedNode.id), updateData);\n                setIsEditing(false);\n                setShowEditDialog(false);\n                await refetchCategories() // 等待刷新完成\n                ;\n                // 显示成功提示\n                toast({\n                    title: \"更新成功\"\n                });\n            }\n        } catch (error) {\n            console.error(\"保存失败:\", error);\n            // 显示错误提示\n            toast({\n                title: \"保存失败\",\n                description: error instanceof Error ? error.message : \"保存时发生未知错误\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleCancel = ()=>{\n        setIsEditing(false);\n        setIsAdding(false);\n        setShowEditDialog(false);\n        setFormData({});\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            placeholder: \"搜索目录\",\n                                            value: searchValue,\n                                            onChange: (e)=>{\n                                                setSearchValue(e.target.value);\n                                                // 实时搜索：输入时自动触发搜索\n                                                if (e.target.value.trim()) {\n                                                    const pathsToExpand = getMatchingNodePaths(catalogTree, e.target.value);\n                                                    setExpandedNodes(pathsToExpand);\n                                                } else {\n                                                    setExpandedNodes([\n                                                        \"1\",\n                                                        \"2\"\n                                                    ]) // 恢复默认展开状态\n                                                    ;\n                                                }\n                                            },\n                                            onKeyDown: (e)=>{\n                                                if (e.key === \"Enter\") {\n                                                    handleSearch();\n                                                }\n                                            },\n                                            className: \"w-80\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: handleResetSearch,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"重置\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleExpandAll,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"展开全部\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleCollapseAll,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"折叠全部\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        onClick: handleAdd,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"新增一级产品目录\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 422,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 421,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0\",\n                    children: categoriesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-muted-foreground\",\n                                children: \"加载中...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 13\n                    }, this) : categoriesError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm mb-2\",\n                                children: [\n                                    \"加载失败: \",\n                                    categoriesError\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: refetchCategories,\n                                children: \"重试\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm border-separate border-spacing-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"bg-muted/30 border-b\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"名称 / 英文名\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-32 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"描述\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-20 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"SKU数量\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"分类编码\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"报关编码\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"属性栏目\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-32 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"更新时间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground\",\n                                                children: \"操作\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: filteredCatalogTree.length > 0 ? filteredCatalogTree.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableTreeNode, {\n                                            node: node,\n                                            level: 0,\n                                            expandedNodes: expandedNodes,\n                                            onToggleExpanded: toggleExpanded,\n                                            onEdit: handleEditNode,\n                                            onDelete: handleDeleteNode,\n                                            onAddChild: handleAddChild,\n                                            searchTerm: searchValue\n                                        }, node.id, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 23\n                                        }, this)) : searchValue.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: 8,\n                                            className: \"p-8 text-center text-muted-foreground\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-8 h-8 text-muted-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"未找到匹配的目录\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: \"请尝试其他关键词或检查拼写\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: 8,\n                                            className: \"p-8 text-center text-muted-foreground\",\n                                            children: \"暂无数据\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 471,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showEditDialog,\n                onOpenChange: setShowEditDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"max-w-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                children: isAdding ? selectedNode ? \"添加\".concat([\n                                    \"二\",\n                                    \"三\",\n                                    \"四\",\n                                    \"五\",\n                                    \"六\",\n                                    \"七\"\n                                ][selectedNodeLevel], \"级产品目录\") : \"添加一级产品目录\" : \"编辑产品目录\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CatalogForm, {\n                            formData: formData,\n                            setFormData: setFormData,\n                            isEditing: true,\n                            isEditingExisting: !isAdding,\n                            onSave: handleSave,\n                            onCancel: handleCancel,\n                            loading: operationLoading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 541,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 540,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n        lineNumber: 419,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCatalogPage, \"90os8Ia1Am+c2vsT027XTEd1NRM=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_11__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast,\n        _hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategoryTree,\n        _hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategories\n    ];\n});\n_c = ProductCatalogPage;\n// 表格树节点组件\nfunction TableTreeNode(param) {\n    let { node, level, expandedNodes, onToggleExpanded, onEdit, onDelete, onAddChild, searchTerm = \"\" } = param;\n    var _node_children;\n    const hasChildren = node.children && node.children.length > 0;\n    const isExpanded = expandedNodes.includes(node.id);\n    // 高亮显示匹配的文本\n    const highlightText = (text, searchTerm)=>{\n        if (!searchTerm.trim()) return text;\n        const regex = new RegExp(\"(\".concat(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \")\"), \"gi\");\n        const parts = text.split(regex);\n        return parts.map((part, index)=>regex.test(part) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n                className: \"bg-yellow-200 text-yellow-900 px-1 rounded\",\n                children: part\n            }, index, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 600,\n                columnNumber: 9\n            }, this) : part);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            style: {\n                                paddingLeft: \"\".concat(level * 20, \"px\")\n                            },\n                            children: [\n                                hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>onToggleExpanded(node.id),\n                                    className: \"p-0.5 hover:bg-muted rounded\",\n                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-foreground\",\n                                            children: highlightText(node.name, searchTerm)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: highlightText(node.englishName, searchTerm)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground truncate max-w-32\",\n                            title: node.description,\n                            children: node.description || \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 642,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: \"outline\",\n                            className: \"text-xs\",\n                            children: node.categoryCode ? highlightText(node.categoryCode, searchTerm) : \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 645,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 650,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 649,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-1\",\n                            children: node.tags.length > 0 ? node.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"text-xs\",\n                                    children: tag\n                                }, index, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 17\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"--\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 653,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 652,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: node.createdAt\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 665,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onEdit(node),\n                                    className: \"h-7 px-2 text-xs text-blue-600 hover:text-blue-800\",\n                                    children: \"编辑\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 13\n                                }, this),\n                                level < 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onAddChild(node, level),\n                                    className: \"h-7 px-2 text-xs text-green-600 hover:text-green-800\",\n                                    children: [\n                                        \"新增\",\n                                        [\n                                            \"二\",\n                                            \"三\",\n                                            \"四\",\n                                            \"五\",\n                                            \"六\",\n                                            \"七\"\n                                        ][level],\n                                        \"级目录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onDelete(node),\n                                    className: \"h-7 px-2 text-xs text-red-600 hover:text-red-800\",\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 671,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 670,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 609,\n                columnNumber: 7\n            }, this),\n            hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: (_node_children = node.children) === null || _node_children === void 0 ? void 0 : _node_children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableTreeNode, {\n                        node: child,\n                        level: level + 1,\n                        expandedNodes: expandedNodes,\n                        onToggleExpanded: onToggleExpanded,\n                        onEdit: onEdit,\n                        onDelete: onDelete,\n                        onAddChild: onAddChild,\n                        searchTerm: searchTerm\n                    }, child.id, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 705,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false)\n        ]\n    }, void 0, true);\n}\n_c1 = TableTreeNode;\n// 分类表单组件\nfunction CatalogForm(param) {\n    let { formData, setFormData, isEditing, isEditingExisting = false, onSave, onCancel, loading } = param;\n    const availableTags = [\n        \"color\",\n        \"size\",\n        \"model\",\n        \"material\",\n        \"style\"\n    ];\n    const updateFormData = (field, value)=>{\n        setFormData({\n            ...formData,\n            [field]: value\n        });\n    };\n    if (isEditing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"name\",\n                                    children: [\n                                        \"中文名称 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 40\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 752,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"name\",\n                                    value: formData.name || \"\",\n                                    onChange: (e)=>updateFormData(\"name\", e.target.value),\n                                    placeholder: \"请输入中文名称\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 751,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"englishName\",\n                                    children: [\n                                        \"英文名称 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 47\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 762,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"englishName\",\n                                    value: formData.englishName || \"\",\n                                    onChange: (e)=>updateFormData(\"englishName\", e.target.value),\n                                    placeholder: \"请输入英文名称\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 763,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 761,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 750,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"categoryCode\",\n                                    children: [\n                                        \"分类编码 \",\n                                        isEditingExisting ? \"(不可修改)\" : \"(可选)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"categoryCode\",\n                                    value: formData.categoryCode || \"\",\n                                    onChange: (e)=>{\n                                        if (!isEditingExisting) {\n                                            // 只有在创建新分类时才允许修改\n                                            const value = e.target.value.toUpperCase().replace(/[^A-Z0-9_-]/g, \"\");\n                                            updateFormData(\"categoryCode\", value);\n                                        }\n                                    },\n                                    placeholder: isEditingExisting ? \"分类编码创建后不可修改\" : \"如：CLOTHING\",\n                                    disabled: isEditingExisting,\n                                    className: isEditingExisting ? \"bg-muted cursor-not-allowed\" : \"\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: isEditingExisting ? \"分类编码在创建后不能修改，以确保数据一致性\" : \"用于自动生成SKU，支持字母数字下划线连字符，会自动转换为大写\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 792,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 774,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    htmlFor: \"status\",\n                                    children: \"状态\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 800,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                    value: formData.status || \"active\",\n                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                placeholder: \"选择状态\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 806,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"active\",\n                                                    children: \"启用\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"inactive\",\n                                                    children: \"禁用\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 801,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 799,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 773,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                            htmlFor: \"description\",\n                            children: \"描述\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 817,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                            id: \"description\",\n                            value: formData.description || \"\",\n                            onChange: (e)=>updateFormData(\"description\", e.target.value),\n                            placeholder: \"请输入目录描述\",\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 818,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 816,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                            htmlFor: \"tags\",\n                            children: \"属性标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 828,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                            id: \"tags\",\n                            value: Array.isArray(formData.tags) ? formData.tags.join(\", \") : \"\",\n                            onChange: (e)=>updateFormData(\"tags\", e.target.value.split(\",\").map((t)=>t.trim()).filter((t)=>t)),\n                            placeholder: \"color, size\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 829,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: [\n                                \"可选标签: \",\n                                availableTags.join(\", \")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 835,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 827,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                            id: \"autoSku\",\n                            checked: !!formData.autoSku,\n                            onCheckedChange: (checked)=>updateFormData(\"autoSku\", !!checked)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 841,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                            htmlFor: \"autoSku\",\n                            children: \"自动生成SKU\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 846,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 840,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-end gap-2 pt-4 border-t\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            onClick: onCancel,\n                            disabled: loading,\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 851,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            onClick: onSave,\n                            disabled: loading,\n                            children: loading ? \"保存中...\" : \"确定\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 859,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 850,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n            lineNumber: 749,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n_c2 = CatalogForm;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ProductCatalogPage\");\n$RefreshReg$(_c1, \"TableTreeNode\");\n$RefreshReg$(_c2, \"CatalogForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/products/product-catalog-page.tsx\n"));

/***/ })

});