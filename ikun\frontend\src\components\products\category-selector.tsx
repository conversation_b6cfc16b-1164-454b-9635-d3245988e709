'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Check, ChevronDown, FolderTree, X, Search } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useCategoryTree } from '@/hooks/useCategories'
import { ProductCategory } from '@/types'

// 类目节点接口
interface CategoryNode {
  id: string
  name: string
  englishName: string
  level: number
  path: string // 完整路径，如 "clothing/mens/tshirts"
  displayPath: string // 显示路径，如 "服装（Clothing）-男装（Mens）-T恤（Tshirts）"
  children?: CategoryNode[]
}

// 将API数据转换为选择器格式
const convertCategoryToNode = (category: ProductCategory, level: number = 1, parentPath: string = '', parentDisplayPath: string = ''): CategoryNode[] => {
  const currentPath = parentPath ? `${parentPath}/${category.category__name.toLowerCase()}` : category.category__name.toLowerCase()
  const currentDisplayPath = parentDisplayPath
    ? `${parentDisplayPath}-${category.category__namecn}（${category.category__name}）`
    : `${category.category__namecn}（${category.category__name}）`

  const node: CategoryNode = {
    id: category.id.toString(),
    name: category.category__namecn,
    englishName: category.category__name,
    level,
    path: currentPath,
    displayPath: currentDisplayPath,
  }

  const result: CategoryNode[] = []
  
  // 递归处理子分类
  if (category.children && category.children.length > 0) {
    const childNodes = category.children.flatMap(child =>
      convertCategoryToNode(child, level + 1, currentPath, currentDisplayPath)
    )
    result.push(...childNodes)
  } else {
    // 如果没有子分类，说明是叶子节点，可以选择
    result.push(node)
  }

  return result
}

// 扁平化所有可选择的分类（只有三级分类可选）
const flattenCategories = (categories: ProductCategory[]): CategoryNode[] => {
  return categories.flatMap(cat => convertCategoryToNode(cat))
}

interface CategorySelectorProps {
  value?: string // 选中的分类路径
  onValueChange: (value: string, displayPath: string) => void
  placeholder?: string
  className?: string
  error?: boolean
}

export function CategorySelector({ 
  value, 
  onValueChange, 
  placeholder = "选择产品类目...",
  className,
  error = false
}: CategorySelectorProps) {
  const [open, setOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  
  // 获取分类数据
  const {
    categoryTree: apiCategoryTree,
    loading: categoriesLoading,
    error: categoriesError
  } = useCategoryTree()

  // 转换为可选择的分类列表
  const selectableCategories = apiCategoryTree ? flattenCategories(apiCategoryTree) : []

  // 过滤分类（支持中文名和英文名搜索）
  const filteredCategories = selectableCategories.filter(category => {
    if (!searchValue.trim()) return true
    const searchTerm = searchValue.toLowerCase()
    return (
      category.name.toLowerCase().includes(searchTerm) ||
      category.englishName.toLowerCase().includes(searchTerm) ||
      category.displayPath.toLowerCase().includes(searchTerm)
    )
  })

  // 获取当前选中分类的显示信息
  const selectedCategory = selectableCategories.find(cat => cat.path === value)

  const handleSelect = (category: CategoryNode) => {
    onValueChange(category.path, category.displayPath)
    setOpen(false)
    setSearchValue('')
  }

  const handleClear = () => {
    onValueChange('', '')
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between",
            !selectedCategory && "text-muted-foreground",
            error && "border-red-500",
            className
          )}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <FolderTree className="w-4 h-4 flex-shrink-0" />
            <span className="truncate">
              {selectedCategory ? selectedCategory.displayPath : placeholder}
            </span>
          </div>
          <div className="flex items-center gap-1 flex-shrink-0">
            {selectedCategory && (
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={(e) => {
                  e.stopPropagation()
                  handleClear()
                }}
              >
                <X className="w-3 h-3" />
              </Button>
            )}
            <ChevronDown className="w-4 h-4" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[600px] p-0" align="start">
        <div className="flex flex-col">
          {/* 搜索框 */}
          <div className="flex items-center border-b px-3 py-2">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <Input
              placeholder="搜索类目（支持中文名/英文名）..."
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              className="border-0 bg-transparent p-0 text-sm outline-none focus-visible:ring-0"
            />
          </div>

          {/* 列表内容 */}
          <div className="max-h-[300px] overflow-y-auto">
            {categoriesLoading ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                加载中...
              </div>
            ) : categoriesError ? (
              <div className="p-4 text-center text-sm text-red-500">
                加载失败: {categoriesError}
              </div>
            ) : filteredCategories.length === 0 ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                {searchValue ? '未找到匹配的类目' : '暂无可选择的类目'}
              </div>
            ) : (
              <div className="p-1">
                {filteredCategories.map((category) => (
                  <div
                    key={category.id}
                    className="flex items-center gap-2 p-3 cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-sm"
                    onClick={() => handleSelect(category)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === category.path ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium truncate">
                        {category.displayPath}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        路径: {category.path}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
