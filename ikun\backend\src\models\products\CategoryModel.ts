/**
 * Product Category Model
 * 产品目录数据模型 - 支持三级分类和树形结构
 */

import { BaseModel, QueryOptions, PaginatedResult } from '@/models/base/BaseModel';
import { ITreeModel, IRelationalModel, RelationType, RelationConfig } from '@/models/base/ModelInterface';
import { ProductCategory } from '@/types';
import { database } from '@/database/connection';
import { logger } from '@/utils/logger';
import { BadRequestError } from '@/middleware/error/errorHandler';

export class CategoryModel extends BaseModel<ProductCategory> implements ITreeModel<ProductCategory>, IRelationalModel<ProductCategory> {
  protected tableName = 'product_categories';
  protected primaryKey = 'id';
  protected timestamps = true;
  
  protected fillable = [
    'category_code',
    'chinese_name',
    'english_name',
    'status',
    'category_level',
    'parent_id',
    'category_description',
    'attribute_tags',
    'category_path'
  ];

  protected hidden: string[] = [];

  // 定义关联关系
  protected relations: Record<string, RelationConfig> = {
    parent: {
      type: RelationType.BELONGS_TO,
      model: 'CategoryModel',
      foreignKey: 'parent_id',
      localKey: 'id'
    },
    children: {
      type: RelationType.HAS_MANY,
      model: 'CategoryModel',
      foreignKey: 'parent_id',
      localKey: 'id'
    },
    products: {
      type: RelationType.HAS_MANY,
      model: 'ProductModel',
      foreignKey: 'category_id',
      localKey: 'id'
    }
  };

  /**
   * 获取关联关系配置
   */
  public getRelation(relationName: string): RelationConfig | undefined {
    return this.relations[relationName];
  }

  /**
   * 根据层级获取分类
   */
  public async findByLevel(level: 1 | 2 | 3 | 4 | 5 | 6 | 7, options: QueryOptions = {}): Promise<ProductCategory[]> {
    const whereCondition = { category_level: level, ...options.where };
    return await this.findMany({ ...options, where: whereCondition });
  }

  /**
   * 根据状态获取分类
   */
  public async findByStatus(status: 'enabled' | 'disabled', options: QueryOptions = {}): Promise<ProductCategory[]> {
    const whereCondition = { status, ...options.where };
    return await this.findMany({ ...options, where: whereCondition });
  }

  /**
   * 根据分类编码查找
   */
  public async findByCode(categoryCode: string): Promise<ProductCategory | null> {
    return await this.findOne({ category_code: categoryCode });
  }

  /**
   * 检查分类编码是否存在
   */
  public async codeExists(categoryCode: string, excludeId?: number): Promise<boolean> {
    const where: any = { category_code: categoryCode };
    if (excludeId) {
      const sql = `SELECT COUNT(*) as count FROM ${this.tableName} WHERE category_code = ? AND id != ?`;
      const result = await database.queryOne<{ count: number }>(sql, [categoryCode, excludeId]);
      return (result?.count || 0) > 0;
    }
    
    const result = await this.findOne(where);
    return result !== null;
  }

  /**
   * 获取子分类 (树形结构接口实现)
   */
  public async getChildren(parentId: number | string): Promise<ProductCategory[]> {
    return await this.findMany({
      where: { parent_id: parentId },
      orderBy: 'english_name',
      orderDirection: 'ASC'
    });
  }

  /**
   * 获取父分类 (树形结构接口实现)
   */
  public async getParent(id: number | string): Promise<ProductCategory | null> {
    const category = await this.findById(id);
    if (!category || !category.parent_id) {
      return null;
    }
    return await this.findById(category.parent_id);
  }

  /**
   * 获取所有祖先分类 (树形结构接口实现)
   */
  public async getAncestors(id: number | string): Promise<ProductCategory[]> {
    const ancestors: ProductCategory[] = [];
    let currentId: number | string | null = id;

    while (currentId) {
      const category = await this.findById(currentId);
      if (!category || !category.parent_id) {
        break;
      }
      
      const parent = await this.findById(category.parent_id);
      if (parent) {
        ancestors.unshift(parent); // 添加到数组开头，保持层级顺序
        currentId = parent.parent_id;
      } else {
        break;
      }
    }

    return ancestors;
  }

  /**
   * 获取所有后代分类 (树形结构接口实现)
   */
  public async getDescendants(id: number | string): Promise<ProductCategory[]> {
    const descendants: ProductCategory[] = [];
    
    const getChildrenRecursive = async (parentId: number | string) => {
      const children = await this.getChildren(parentId);
      for (const child of children) {
        descendants.push(child);
        await getChildrenRecursive(child.id);
      }
    };

    await getChildrenRecursive(id);
    return descendants;
  }

  /**
   * 构建分类树 (树形结构接口实现)
   */
  public async buildTree(items?: ProductCategory[]): Promise<any[]> {
    if (!items) {
      items = await this.findMany({
        where: { status: 'enabled' },
        orderBy: 'category_level, english_name',
        orderDirection: 'ASC'
      });
    }

    const tree: any[] = [];
    const itemMap = new Map<number, any>();

    // 创建节点映射 - 统一转换为数字类型确保类型一致
    items.forEach(item => {
      itemMap.set(Number(item.id), {
        ...item,
        children: []
      });
    });

    // 构建树形结构 - 统一转换为数字类型进行比较
    items.forEach(item => {
      const node = itemMap.get(Number(item.id));
      if (item.parent_id && itemMap.has(Number(item.parent_id))) {
        const parent = itemMap.get(Number(item.parent_id));
        parent.children.push(node);
      } else {
        tree.push(node);
      }
    });

    return tree;
  }

  /**
   * 获取分类层级 (树形结构接口实现)
   */
  public async getLevel(id: number | string): Promise<number> {
    const category = await this.findById(id);
    return category?.category_level || 0;
  }

  /**
   * 生成分类路径
   */
  public async generateCategoryPath(parentId: number | null, englishName: string): Promise<string> {
    if (!parentId) {
      return englishName.toLowerCase().replace(/\s+/g, '-');
    }

    const parent = await this.findById(parentId);
    if (!parent) {
      throw new BadRequestError('Parent category not found');
    }

    return `${parent.category_path}/${englishName.toLowerCase().replace(/\s+/g, '-')}`;
  }

  /**
   * 验证分类层级关系
   */
  public async validateHierarchy(categoryLevel: number, parentId: number | null): Promise<void> {
    // 验证层级范围
    if (categoryLevel < 1 || categoryLevel > 7) {
      throw new BadRequestError('Category level must be between 1 and 7');
    }

    if (categoryLevel === 1 && parentId !== null) {
      throw new BadRequestError('Level 1 categories cannot have a parent');
    }

    if (categoryLevel > 1 && !parentId) {
      throw new BadRequestError(`Level ${categoryLevel} categories must have a parent`);
    }

    if (parentId) {
      const parent = await this.findById(parentId);
      if (!parent) {
        throw new BadRequestError('Parent category not found');
      }

      if (parent.category_level !== categoryLevel - 1) {
        throw new BadRequestError(`Invalid category level. Parent is level ${parent.category_level}, child should be level ${parent.category_level + 1}`);
      }

      // 确保不会超过最大层级
      if (parent.category_level >= 7) {
        throw new BadRequestError('Cannot create subcategory: maximum level (7) reached');
      }
    }
  }

  /**
   * 检查是否可以删除分类
   */
  public async canDelete(id: number | string): Promise<{ canDelete: boolean; reason?: string }> {
    // 检查是否有子分类
    const children = await this.getChildren(id);
    if (children.length > 0) {
      return {
        canDelete: false,
        reason: 'Category has child categories'
      };
    }

    // 检查是否有关联的产品
    const productCount = await database.queryOne<{ count: number }>(
      'SELECT COUNT(*) as count FROM product_dropship WHERE category_id = ?',
      [id]
    );

    if (productCount && productCount.count > 0) {
      return {
        canDelete: false,
        reason: 'Category has associated products'
      };
    }

    return { canDelete: true };
  }

  /**
   * 批量创建分类
   */
  public async batchCreate(categories: Partial<ProductCategory>[]): Promise<ProductCategory[]> {
    const results: ProductCategory[] = [];

    await this.transaction(async (connection) => {
      for (const categoryData of categories) {
        // 验证层级关系
        if (categoryData.category_level && categoryData.parent_id !== undefined) {
          await this.validateHierarchy(categoryData.category_level, categoryData.parent_id);
        }

        // 生成分类路径
        if (categoryData.english_name) {
          categoryData.category_path = await this.generateCategoryPath(
            categoryData.parent_id || null,
            categoryData.english_name
          );
        }

        const category = await this.create(categoryData);
        results.push(category);
      }
    });

    return results;
  }

  /**
   * 更新分类状态
   */
  public async updateStatus(id: number | string, status: 'enabled' | 'disabled'): Promise<ProductCategory> {
    return await this.update(id, { status } as Partial<ProductCategory>);
  }



  /**
   * 搜索分类
   */
  public async search(query: string, options: QueryOptions = {}): Promise<PaginatedResult<ProductCategory>> {
    const searchConditions = `
      (chinese_name LIKE ? OR english_name LIKE ? OR category_code LIKE ? OR category_description LIKE ?)
    `;
    
    const searchParams = Array(4).fill(`%${query}%`);
    
    const { page = 1, limit = 20 } = options;
    const offset = (page - 1) * limit;

    // 获取总数
    const countSql = `
      SELECT COUNT(*) as total FROM ${this.tableName} 
      WHERE ${searchConditions}
    `;
    const countResult = await database.queryOne<{ total: number }>(countSql, searchParams);
    const total = countResult?.total || 0;

    // 获取数据
    const sql = `
      SELECT * FROM ${this.tableName}
      WHERE ${searchConditions}
      ORDER BY category_level ASC, english_name ASC, created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const items = await database.query<ProductCategory>(sql, [...searchParams, limit, offset]);

    return {
      items: items.map(item => this.formatOutput(item)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * 关联查询实现
   */
  public async with(relations: string[]): Promise<ProductCategory[]> {
    // TODO: 实现关联查询逻辑
    throw new Error('Relation loading not implemented yet');
  }

  public async findWithRelations(id: number | string, relations: string[]): Promise<ProductCategory | null> {
    // TODO: 实现单个记录的关联查询
    throw new Error('Relation loading not implemented yet');
  }

  /**
   * 格式化输出数据
   */
  protected formatOutput(data: ProductCategory): ProductCategory {
    const formatted = super.formatOutput(data);
    
    // 解析JSON字段
    if (formatted.attribute_tags && typeof formatted.attribute_tags === 'string') {
      try {
        formatted.attribute_tags = JSON.parse(formatted.attribute_tags as string);
      } catch (error) {
        logger.warn('Failed to parse attribute_tags:', { categoryId: formatted.id, error });
        formatted.attribute_tags = [];
      }
    }
    
    return formatted;
  }

  /**
   * 准备插入数据
   */
  protected prepareForInsert(data: Partial<ProductCategory>): Record<string, any> {
    const prepared = super.prepareForInsert(data);
    
    // 序列化JSON字段
    if (prepared.attribute_tags && Array.isArray(prepared.attribute_tags)) {
      prepared.attribute_tags = JSON.stringify(prepared.attribute_tags);
    }
    
    return prepared;
  }

  /**
   * 准备更新数据
   */
  protected prepareForUpdate(data: Partial<ProductCategory>): Record<string, any> {
    const prepared = super.prepareForUpdate(data);

    // 序列化JSON字段
    if (prepared.attribute_tags && Array.isArray(prepared.attribute_tags)) {
      prepared.attribute_tags = JSON.stringify(prepared.attribute_tags);
    }

    return prepared;
  }


}

export default CategoryModel;
