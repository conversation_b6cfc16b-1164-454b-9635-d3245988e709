/**
 * Product Category Model
 * 产品目录数据模型 - 支持三级分类和树形结构
 */

import { BaseModel, QueryOptions, PaginatedResult } from '@/models/base/BaseModel';
import { ITreeModel, IRelationalModel, RelationType, RelationConfig } from '@/models/base/ModelInterface';
import { ProductCategory } from '@/types';
import { database } from '@/database/connection';
import { logger } from '@/utils/logger';
import { BadRequestError } from '@/middleware/error/errorHandler';

export class CategoryModel extends BaseModel<ProductCategory> implements ITreeModel<ProductCategory>, IRelationalModel<ProductCategory> {
  protected tableName = 'product_categories';
  protected primaryKey = 'id';
  protected timestamps = true;
  
  protected fillable = [
    'category_platform',
    'category_code',
    'category__namecn',
    'category__name',
    'category_status',
    'category_level',
    'category_parent_id',
    'category_description',
    'category_attribute_tags',
    'category_path',
    'category__mapping_local'
  ];

  protected hidden: string[] = [];

  // 定义关联关系
  protected relations: Record<string, RelationConfig> = {
    parent: {
      type: RelationType.BELONGS_TO,
      model: 'CategoryModel',
      foreignKey: 'category_parent_id',
      localKey: 'id'
    },
    children: {
      type: RelationType.HAS_MANY,
      model: 'CategoryModel',
      foreignKey: 'category_parent_id',
      localKey: 'id'
    },
    products: {
      type: RelationType.HAS_MANY,
      model: 'ProductModel',
      foreignKey: 'category_id',
      localKey: 'id'
    }
  };

  /**
   * 获取关联关系配置
   */
  public getRelation(relationName: string): RelationConfig | undefined {
    return this.relations[relationName];
  }

  /**
   * 根据层级获取分类
   */
  public async findByLevel(level: 1 | 2 | 3 | 4 | 5 | 6 | 7, options: QueryOptions = {}): Promise<ProductCategory[]> {
    const whereCondition = { category_level: level, ...options.where };
    return await this.findMany({ ...options, where: whereCondition });
  }

  /**
   * 根据状态获取分类
   */
  public async findByStatus(status: 'enabled' | 'disabled', options: QueryOptions = {}): Promise<ProductCategory[]> {
    const whereCondition = { category_status: status, ...options.where };
    return await this.findMany({ ...options, where: whereCondition });
  }

  /**
   * 获取所有分类（不分页，用于构建分类树）
   */
  public async findAllCategories(): Promise<ProductCategory[]> {
    try {
      const sql = `
        SELECT * FROM ${this.tableName}
        WHERE category_status = 'enabled'
        ORDER BY category_level ASC, category__name ASC
      `;

      const results = await database.query<ProductCategory>(sql);
      return results.map(item => this.formatOutput(item));
    } catch (error) {
      logger.error(`Error finding all categories:`, error);
      throw error;
    }
  }

  /**
   * 根据层级获取所有分类（不分页）
   */
  public async findCategoriesByLevel(level: 1 | 2 | 3 | 4 | 5 | 6 | 7): Promise<ProductCategory[]> {
    try {
      const sql = `
        SELECT * FROM ${this.tableName}
        WHERE category_status = 'enabled' AND category_level = ?
        ORDER BY category__name ASC
      `;

      const results = await database.query<ProductCategory>(sql, [level]);
      return results.map(item => this.formatOutput(item));
    } catch (error) {
      logger.error(`Error finding categories by level ${level}:`, error);
      throw error;
    }
  }

  /**
   * 检查分类是否被产品使用（包括子分类）
   */
  public async isCategoryInUse(categoryId: number): Promise<{ inUse: boolean; productCount: number; details: string }> {
    try {
      // 获取当前分类及其所有子分类的ID
      const categoryIds = await this.getCategoryAndDescendantIds(categoryId);

      // 检查 product_dropship 表中是否有产品使用这些分类
      const placeholders = categoryIds.map(() => '?').join(',');
      const sql = `
        SELECT COUNT(*) as count
        FROM product_dropship
        WHERE category_id IN (${placeholders})
      `;

      const result = await database.queryOne<{ count: number }>(sql, categoryIds);
      const productCount = result?.count || 0;

      if (productCount > 0) {
        // 获取使用该分类的产品详情（最多显示5个）
        const detailSql = `
          SELECT sku, english_title
          FROM product_dropship
          WHERE category_id IN (${placeholders})
          LIMIT 5
        `;

        const products = await database.query<{ sku: string; english_title: string }>(detailSql, categoryIds);
        const productList = products.map(p => `${p.sku} (${p.english_title})`).join(', ');
        const moreText = productCount > 5 ? ` 等${productCount}个产品` : '';

        return {
          inUse: true,
          productCount,
          details: `该分类被以下产品使用：${productList}${moreText}`
        };
      }

      return {
        inUse: false,
        productCount: 0,
        details: ''
      };
    } catch (error) {
      logger.error(`Error checking category usage for ID ${categoryId}:`, error);
      throw error;
    }
  }

  /**
   * 获取分类及其所有子分类的ID
   */
  private async getCategoryAndDescendantIds(categoryId: number): Promise<number[]> {
    const ids: number[] = [categoryId];

    const getChildrenRecursive = async (parentId: number) => {
      const children = await this.getChildren(parentId);
      for (const child of children) {
        ids.push(Number(child.id));
        await getChildrenRecursive(Number(child.id));
      }
    };

    await getChildrenRecursive(categoryId);
    return ids;
  }

  /**
   * 根据分类编码查找
   */
  public async findByCode(categoryCode: string): Promise<ProductCategory | null> {
    return await this.findOne({ category_code: categoryCode });
  }

  /**
   * 检查分类编码是否存在
   */
  public async codeExists(categoryCode: string, excludeId?: number): Promise<boolean> {
    const where: any = { category_code: categoryCode };
    if (excludeId) {
      const sql = `SELECT COUNT(*) as count FROM ${this.tableName} WHERE category_code = ? AND id != ?`;
      const result = await database.queryOne<{ count: number }>(sql, [categoryCode, excludeId]);
      return (result?.count || 0) > 0;
    }
    
    const result = await this.findOne(where);
    return result !== null;
  }

  /**
   * 获取子分类 (树形结构接口实现)
   */
  public async getChildren(parentId: number | string): Promise<ProductCategory[]> {
    return await this.findMany({
      where: { category_parent_id: parentId },
      orderBy: 'category__name',
      orderDirection: 'ASC'
    });
  }

  /**
   * 获取父分类 (树形结构接口实现)
   */
  public async getParent(id: number | string): Promise<ProductCategory | null> {
    const category = await this.findById(id);
    if (!category || !category.category_parent_id) {
      return null;
    }
    return await this.findById(category.category_parent_id);
  }

  /**
   * 获取所有祖先分类 (树形结构接口实现)
   */
  public async getAncestors(id: number | string): Promise<ProductCategory[]> {
    const ancestors: ProductCategory[] = [];
    let currentId: number | string | null = id;

    while (currentId) {
      const category = await this.findById(currentId);
      if (!category || !category.category_parent_id) {
        break;
      }

      const parent = await this.findById(category.category_parent_id);
      if (parent) {
        ancestors.unshift(parent); // 添加到数组开头，保持层级顺序
        currentId = parent.category_parent_id;
      } else {
        break;
      }
    }

    return ancestors;
  }

  /**
   * 获取所有后代分类 (树形结构接口实现)
   */
  public async getDescendants(id: number | string): Promise<ProductCategory[]> {
    const descendants: ProductCategory[] = [];
    
    const getChildrenRecursive = async (parentId: number | string) => {
      const children = await this.getChildren(parentId);
      for (const child of children) {
        descendants.push(child);
        await getChildrenRecursive(child.id);
      }
    };

    await getChildrenRecursive(id);
    return descendants;
  }

  /**
   * 构建分类树 (树形结构接口实现)
   */
  public async buildTree(items?: ProductCategory[]): Promise<any[]> {
    if (!items) {
      items = await this.findAllCategories();
    }

    const tree: any[] = [];
    const itemMap = new Map<number, any>();

    // 创建节点映射 - 统一转换为数字类型确保类型一致
    items.forEach(item => {
      itemMap.set(Number(item.id), {
        ...item,
        children: []
      });
    });

    // 构建树形结构 - 统一转换为数字类型进行比较
    items.forEach(item => {
      const node = itemMap.get(Number(item.id));
      if (item.category_parent_id && itemMap.has(Number(item.category_parent_id))) {
        const parent = itemMap.get(Number(item.category_parent_id));
        parent.children.push(node);
      } else {
        tree.push(node);
      }
    });

    return tree;
  }

  /**
   * 获取分类层级 (树形结构接口实现)
   */
  public async getLevel(id: number | string): Promise<number> {
    const category = await this.findById(id);
    return category?.category_level || 0;
  }

  /**
   * 生成分类路径
   */
  public async generateCategoryPath(parentId: number | null, englishName: string): Promise<string> {
    if (!parentId) {
      return englishName.toLowerCase().replace(/\s+/g, '-');
    }

    const parent = await this.findById(parentId);
    if (!parent) {
      throw new BadRequestError('Parent category not found');
    }

    return `${parent.category_path}/${englishName.toLowerCase().replace(/\s+/g, '-')}`;
  }

  /**
   * 验证分类层级关系
   */
  public async validateHierarchy(categoryLevel: number, parentId: number | null): Promise<void> {
    // 验证层级范围
    if (categoryLevel < 1 || categoryLevel > 7) {
      throw new BadRequestError('Category level must be between 1 and 7');
    }

    if (categoryLevel === 1 && parentId !== null) {
      throw new BadRequestError('Level 1 categories cannot have a parent');
    }

    if (categoryLevel > 1 && !parentId) {
      throw new BadRequestError(`Level ${categoryLevel} categories must have a parent`);
    }

    if (parentId) {
      const parent = await this.findById(parentId);
      if (!parent) {
        throw new BadRequestError('Parent category not found');
      }

      if (parent.category_level !== categoryLevel - 1) {
        throw new BadRequestError(`Invalid category level. Parent is level ${parent.category_level}, child should be level ${parent.category_level + 1}`);
      }

      // 确保不会超过最大层级
      if (parent.category_level >= 7) {
        throw new BadRequestError('Cannot create subcategory: maximum level (7) reached');
      }
    }
  }

  /**
   * 检查是否可以删除分类
   */
  public async canDelete(id: number | string): Promise<{ canDelete: boolean; reason?: string }> {
    // 检查是否有子分类
    const children = await this.getChildren(id);
    if (children.length > 0) {
      const childNames = children.map(child => `${child.category__namecn}(${child.category__name})`).join(', ');
      return {
        canDelete: false,
        reason: `该分类包含子分类，请先删除子分类：${childNames}`
      };
    }

    // 使用新的详细检查方法
    const usageCheck = await this.isCategoryInUse(Number(id));
    if (usageCheck.inUse) {
      return {
        canDelete: false,
        reason: usageCheck.details
      };
    }

    return { canDelete: true };
  }

  /**
   * 批量创建分类
   */
  public async batchCreate(categories: Partial<ProductCategory>[]): Promise<ProductCategory[]> {
    const results: ProductCategory[] = [];

    await this.transaction(async (connection) => {
      for (const categoryData of categories) {
        // 验证层级关系
        if (categoryData.category_level && categoryData.category_parent_id !== undefined) {
          await this.validateHierarchy(categoryData.category_level, categoryData.category_parent_id);
        }

        // 生成分类路径
        if (categoryData.category__name) {
          categoryData.category_path = await this.generateCategoryPath(
            categoryData.category_parent_id || null,
            categoryData.category__name
          );
        }

        const category = await this.create(categoryData);
        results.push(category);
      }
    });

    return results;
  }

  /**
   * 更新分类状态
   */
  public async updateStatus(id: number | string, status: 'enabled' | 'disabled'): Promise<ProductCategory> {
    return await this.update(id, { category_status: status } as Partial<ProductCategory>);
  }



  /**
   * 搜索分类
   */
  public async search(query: string, options: QueryOptions = {}): Promise<PaginatedResult<ProductCategory>> {
    const searchConditions = `
      (category__namecn LIKE ? OR category__name LIKE ? OR category_code LIKE ? OR category_description LIKE ?)
    `;
    
    const searchParams = Array(4).fill(`%${query}%`);
    
    const { page = 1, limit = 20 } = options;
    const offset = (page - 1) * limit;

    // 获取总数
    const countSql = `
      SELECT COUNT(*) as total FROM ${this.tableName} 
      WHERE ${searchConditions}
    `;
    const countResult = await database.queryOne<{ total: number }>(countSql, searchParams);
    const total = countResult?.total || 0;

    // 获取数据
    const sql = `
      SELECT * FROM ${this.tableName}
      WHERE ${searchConditions}
      ORDER BY category_level ASC, category__name ASC, category_created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const items = await database.query<ProductCategory>(sql, [...searchParams, limit, offset]);

    return {
      items: items.map(item => this.formatOutput(item)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * 关联查询实现
   */
  public async with(relations: string[]): Promise<ProductCategory[]> {
    // TODO: 实现关联查询逻辑
    throw new Error('Relation loading not implemented yet');
  }

  public async findWithRelations(id: number | string, relations: string[]): Promise<ProductCategory | null> {
    // TODO: 实现单个记录的关联查询
    throw new Error('Relation loading not implemented yet');
  }

  /**
   * 格式化输出数据
   */
  protected formatOutput(data: ProductCategory): ProductCategory {
    const formatted = super.formatOutput(data);
    
    // 解析JSON字段
    if (formatted.attribute_tags && typeof formatted.attribute_tags === 'string') {
      try {
        formatted.attribute_tags = JSON.parse(formatted.attribute_tags as string);
      } catch (error) {
        logger.warn('Failed to parse attribute_tags:', { categoryId: formatted.id, error });
        formatted.attribute_tags = [];
      }
    }
    
    return formatted;
  }

  /**
   * 准备插入数据
   */
  protected prepareForInsert(data: Partial<ProductCategory>): Record<string, any> {
    const prepared = super.prepareForInsert(data);
    
    // 序列化JSON字段
    if (prepared.attribute_tags && Array.isArray(prepared.attribute_tags)) {
      prepared.attribute_tags = JSON.stringify(prepared.attribute_tags);
    }
    
    return prepared;
  }

  /**
   * 准备更新数据
   */
  protected prepareForUpdate(data: Partial<ProductCategory>): Record<string, any> {
    const prepared = super.prepareForUpdate(data);

    // 序列化JSON字段
    if (prepared.attribute_tags && Array.isArray(prepared.attribute_tags)) {
      prepared.attribute_tags = JSON.stringify(prepared.attribute_tags);
    }

    return prepared;
  }


}

export default CategoryModel;
