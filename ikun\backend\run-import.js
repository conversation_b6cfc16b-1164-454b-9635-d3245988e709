/**
 * 分类导入执行脚本
 * 使用方法: node run-import.js
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('开始执行分类导入...');

// 使用 ts-node 直接运行 TypeScript 文件
const tsNodePath = path.join(__dirname, 'node_modules', '.bin', 'ts-node');
const scriptPath = path.join(__dirname, 'src', 'database', 'import-categories.ts');

const child = spawn('npx', ['ts-node', scriptPath], {
  stdio: 'inherit',
  cwd: __dirname
});

child.on('close', (code) => {
  if (code === 0) {
    console.log('分类导入完成！');
  } else {
    console.error(`分类导入失败，退出码: ${code}`);
  }
});

child.on('error', (error) => {
  console.error('执行错误:', error);
});
