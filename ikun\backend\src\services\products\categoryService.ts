/**
 * Category Service
 * Business logic for product category management
 */

import { CategoryModel } from '@/models/products/CategoryModel';
import {
  ProductCategory,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  PaginationQuery,
  PaginatedResponse
} from '@/types';
import { logger } from '@/utils/logger';

class CategoryService {
  private categoryModel = new CategoryModel();

  // Get categories with pagination and filters
  public async getCategories(query: PaginationQuery & {
    level?: number;
    parent_id?: number;
    status?: 'enabled' | 'disabled';
  }): Promise<PaginatedResponse<ProductCategory>> {
    const { page = 1, limit = 20, level, parent_id, status } = query;

    const where: any = {};
    if (level) where['category_level'] = level;
    if (parent_id !== undefined) where['parent_id'] = parent_id;
    if (status) where['status'] = status;

    return await this.categoryModel.paginate({
      page,
      limit,
      where,
      orderBy: 'english_name',
      orderDirection: 'ASC'
    });
  }

  // Get category tree structure
  public async getCategoryTree(): Promise<any[]> {
    const categories = await this.categoryModel.findMany({
      where: { status: 'enabled' },
      orderBy: 'english_name',
      orderDirection: 'ASC'
    });

    return await this.categoryModel.buildTree(categories);
  }

  // Get all categories (for Excel template generation)
  public async getAllCategories(): Promise<any[]> {
    return await this.getCategoryTree();
  }

  // Get categories by level
  public async getCategoriesByLevel(level: 1 | 2 | 3 | 4 | 5 | 6 | 7): Promise<ProductCategory[]> {
    return await this.categoryModel.findByLevel(level, {
      where: { status: 'enabled' },
      orderBy: 'english_name',
      orderDirection: 'ASC'
    });
  }

  // Create new category
  public async createCategory(data: CreateCategoryRequest): Promise<ProductCategory> {
    // Validate hierarchy
    await this.categoryModel.validateHierarchy(data.category_level, data.parent_id || null);

    // 处理分类编码：如果提供了编码，检查唯一性；如果没有提供，则保持为空
    let categoryCode = data.category_code?.trim() || null;
    if (categoryCode) {
      // 如果提供了分类编码，检查是否已存在
      if (await this.categoryModel.codeExists(categoryCode)) {
        throw new Error(`Category code '${categoryCode}' already exists`);
      }
    }

    // Generate category path
    const categoryPath = await this.categoryModel.generateCategoryPath(
      data.parent_id || null,
      data.english_name
    );

    // 构建分类数据，分类编码可以为空
    const categoryData = {
      ...data,
      category_code: categoryCode,
      category_path: categoryPath
    };

    const category = await this.categoryModel.create(categoryData);
    logger.info('Category created:', {
      categoryId: category.id,
      code: category.category_code || 'null'
    });

    return category;
  }



  // Batch create categories
  public async batchCreateCategories(categories: CreateCategoryRequest[]): Promise<ProductCategory[]> {
    return await this.categoryModel.batchCreate(categories);
  }

  // Get category by ID
  public async getCategoryById(id: number): Promise<ProductCategory> {
    return await this.categoryModel.findByIdOrFail(id);
  }

  // Update category
  public async updateCategory(id: number, data: UpdateCategoryRequest): Promise<ProductCategory> {
    const category = await this.categoryModel.update(id, data);
    logger.info('Category updated:', { categoryId: id });
    return category;
  }

  // Update category status
  public async updateCategoryStatus(id: number, status: 'enabled' | 'disabled'): Promise<ProductCategory> {
    const category = await this.categoryModel.updateStatus(id, status);
    logger.info('Category status updated:', { categoryId: id, status });
    return category;
  }

  // Delete category
  public async deleteCategory(id: number): Promise<void> {
    const canDelete = await this.categoryModel.canDelete(id);
    if (!canDelete.canDelete) {
      throw new Error(canDelete.reason || 'Cannot delete category');
    }

    await this.categoryModel.delete(id);
    logger.info('Category deleted:', { categoryId: id });
  }

  // Get category children
  public async getCategoryChildren(id: number): Promise<ProductCategory[]> {
    return await this.categoryModel.getChildren(id);
  }
}

export const categoryService = new CategoryService();
