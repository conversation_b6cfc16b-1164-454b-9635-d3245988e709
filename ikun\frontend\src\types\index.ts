// 产品分类类型
export interface ProductCategory {
  id: number
  category_code: string | null
  chinese_name: string
  english_name: string
  status: 'enabled' | 'disabled'
  auto_sku: 'enabled' | 'disabled'
  category_level: 1 | 2 | 3 | 4 | 5 | 6 | 7
  parent_id: number | null
  category_description?: string
  attribute_tags: string[]
  sort_order: number
  category_path: string
  created_at: string
  updated_at: string
  children?: ProductCategory[]
}

// 铺货产品类型
export interface DropshipProduct {
  id: number
  source: string
  sku: string
  ean: string
  category_id: number
  category: string
  english_title: string
  english_description: string
  selling_point?: string[]
  image1: string
  image2?: string
  image3?: string
  image4?: string
  image5?: string
  cost_price?: number
  package_weight?: number
  package_length?: number
  package_width?: number
  package_height?: number
  purchase_link?: string
  claim_time?: string
  claim_platform?: string
  listing_count: number
  remarks?: string
  status: 'draft' | 'active' | 'inactive'
  created_at: string
  updated_at: string
}

// 产品相关类型（保持兼容性）
export interface Product {
  id: string
  sku: string
  title: string
  description: string
  category: string
  price: number
  cost: number
  weight: number
  dimensions: {
    length: number
    width: number
    height: number
  }
  images: string[]
  tags: string[]
  status: 'active' | 'inactive' | 'draft'
  createdAt: string
  updatedAt: string
}

// 订单相关类型
export interface Order {
  id: string
  platformOrderId: string
  platform: string
  customerId: string
  items: OrderItem[]
  totalAmount: number
  status: 'pending' | 'paid' | 'shipped' | 'delivered' | 'cancelled'
  shippingAddress: Address
  createdAt: string
  updatedAt: string
}

export interface OrderItem {
  id: string
  productId: string
  sku: string
  title: string
  quantity: number
  price: number
}

export interface Address {
  name: string
  street: string
  city: string
  state: string
  country: string
  postalCode: string
}

// 平台类型
export interface Platform {
  id: number
  platform_code: string
  platform_name: string
  platform_name_en: string
  logo_url?: string
  status: 'active' | 'inactive'
  sort_order: number
  config_fields?: any
  description?: string
  created_at: string
  updated_at: string
}

// 店铺相关类型
export interface Store {
  id: number
  platform_code: string
  store_name: string
  status: 'active' | 'failed'
  api_key?: string
  token?: string
  token_expires_at?: Date
  site?: string
  platform_config?: any
  created_at: string
  updated_at: string
  // 关联的平台信息
  platform_name?: string
  platform_name_en?: string
  logo_url?: string
  config_fields?: any
}

// 旧的店铺类型（保持兼容性）
export interface LegacyStore {
  id: string
  sellerId: string
  name: string
  username: string
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
  token?: string
  tokenExpiresAt?: string
}

// 任务相关类型
export interface Task {
  id: string
  type: 'translation' | 'publish' | 'scraping'
  sellerId: string
  storeName: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  totalItems: number
  processedItems: number
  batchSize: number
  interval: number
  priority: number
  description?: string
  createdAt: string
  updatedAt: string
  nextRunAt?: string
  lastRunAt?: string
}

// 采集任务类型
export interface ScrapingTask {
  id: string
  platform: string
  url: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  result?: any
  errorMessage?: string
  createdAt: string
  completedAt?: string
}

// API响应类型
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
  timestamp: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 分页查询参数
export interface PaginationQuery {
  page?: number
  limit?: number
}

// 分页结果
export interface PaginationResult<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// 搜索查询参数
export interface SearchParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  category_id?: number
  source?: string
  start_date?: string
  end_date?: string
}

// 表单类型
export interface ProductFormData {
  sku: string
  title: string
  description: string
  category: string
  price: number
  cost: number
  weight: number
  dimensions: {
    length: number
    width: number
    height: number
  }
  images: string[]
  tags: string[]
  status: 'active' | 'inactive' | 'draft'
}

export interface StoreFormData {
  name: string
  sellerId: string
  username: string
  password: string
}

export interface TaskFormData {
  storeId: string
  type: 'translation' | 'publish'
  batchSize: number
  interval: number
  priority: number
  description?: string
}

// 产品上架相关类型
export interface UploadProductListing {
  id: number
  // 关联字段
  dropship_product_id: number
  platform_code: string
  store_id: number
  platform_category_id?: string
  // 冗余关键基础信息
  sku: string
  ean?: string
  english_title?: string
  image1?: string
  // 上架平台特定信息
  upstores_sku?: string
  upstores_ean?: string
  // 多语言内容
  multi_titles?: Record<string, string>
  multi_descriptions?: Record<string, string>
  multi_selling_points?: Record<string, string[]>
  listings_translation_status: 'pending' | 'completed'
  // 平台特定参数
  platform_data?: any
  platform_attributes?: any
  attributes_status: 'current' | 'outdated' | 'needs_review'
  // 销售参数
  discounted_price?: number
  original_price?: number
  discount_end_date?: string
  discount_start_date?: string
  stock_quantity: number
  discount?: number
  currency: string
  // 状态管理
  status: 'draft' | 'pending' | 'active' | 'failed' | 'inactive'
  listing_id?: string
  // 错误和同步信息
  error_message?: string
  last_sync_at?: string
  // 时间戳
  uplisting_at: string
  created_at: string
  updated_at: string
}

export interface UploadProductCategory {
  id: number
  platform_code: string
  category_id: string
  parent_category_id?: string
  category_name?: string
  category_name_cn?: string
  category_path?: string
  level: number
  is_leaf: boolean
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

export interface UploadProductCategoryAttribute {
  id: number
  platform_code: string
  category_id: string
  attribute_name: string
  attribute_name_cn?: string
  attribute_label?: any
  attribute_type: 'text' | 'number' | 'select' | 'multiselect' | 'boolean' | 'date'
  is_required: boolean
  is_variation: boolean
  max_length?: number
  min_value?: number
  max_value?: number
  options?: string[]
  options_cn?: string[]
  has_predefined_options: boolean
  sort_order: number
  group_name?: string
  group_name_cn?: string
  translation_status: 'pending' | 'completed' | 'failed'
  created_at: string
  updated_at: string
}

export interface CreateUploadProductData {
  dropship_product_id: number
  store_id: number
  platform_category_id?: string
  sku: string
  ean?: string
  english_title?: string
  image1?: string
  upstores_sku?: string
  upstores_ean?: string
  multi_titles?: Record<string, string>
  multi_descriptions?: Record<string, string>
  platform_data?: any
  platform_attributes?: any
  discounted_price?: number
  original_price?: number
  discount_end_date?: string
  discount_start_date?: string
  stock_quantity?: number
  discount?: number
  currency?: string
  status?: 'draft' | 'pending' | 'active' | 'failed' | 'inactive'
}

export interface UpdateUploadProductData {
  platform_category_id?: string
  sku?: string
  ean?: string
  english_title?: string
  image1?: string
  upstores_sku?: string
  upstores_ean?: string
  multi_titles?: Record<string, string>
  multi_descriptions?: Record<string, string>
  platform_data?: any
  platform_attributes?: any
  discounted_price?: number
  original_price?: number
  discount_end_date?: string
  discount_start_date?: string
  stock_quantity?: number
  discount?: number
  currency?: string
  status?: 'draft' | 'pending' | 'active' | 'failed' | 'inactive'
}

// 翻译相关类型
export type LanguageCode = 'en' | 'zh' | 'lt' | 'lv' | 'et' | 'fi' | 'pt' | 'es'

export interface TranslationRequest {
  text: string
  sourceLang: LanguageCode
  targetLang?: LanguageCode
  targetLangs?: LanguageCode[]
  contentType?: 'title' | 'description' | 'selling_point'
  provider?: string
}

export interface TranslationResponse {
  success: boolean
  text?: string
  translations?: Record<string, string>
  errors?: Record<string, string>
  error?: string
}

export interface ProductTranslationRequest {
  title: string
  description: string
  sellingPoints?: string[]
  sourceLang: LanguageCode
  targetLangs: LanguageCode[]
  provider?: string
}

export interface ProductTranslationResponse {
  success: boolean
  title?: Record<string, string>
  description?: Record<string, string>
  sellingPoints?: Record<string, string[]>
  errors?: string[]
}

export interface TranslationConfig {
  defaultProvider: string
  providers: Record<string, any>
  defaultSourceLang: LanguageCode
  defaultTargetLangs: LanguageCode[]
  enableCache: boolean
  cacheExpiry: number
}

export interface TranslationStats {
  totalRequests: number
  successCount: number
  failureCount: number
  averageResponseTime: number
  lastRequestTime: string
}
