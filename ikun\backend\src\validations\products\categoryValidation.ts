/**
 * Category Validation Schemas
 * Joi validation rules for category-related requests
 */

import <PERSON><PERSON> from 'joi';

export const categoryValidation = {
  // GET /api/v1/categories
  getCategories: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    level: Joi.number().integer().valid(1, 2, 3, 4, 5, 6, 7).optional(),
    parent_id: Joi.number().integer().positive().optional(),
    status: Joi.string().valid('enabled', 'disabled').optional()
  }),

  // GET /api/v1/categories/level/:level
  getByLevel: Joi.object({
    level: Joi.number().integer().valid(1, 2, 3, 4, 5, 6, 7).required()
  }),

  // POST /api/v1/categories
  createCategory: Joi.object({
    category_code: Joi.string().trim().max(50).optional().allow('')
      .pattern(/^[A-Za-z0-9-_]*$/)
      .uppercase()
      .messages({
        'string.pattern.base': 'Category code can only contain letters, numbers, hyphens, and underscores'
      }),
    chinese_name: Joi.string().trim().min(1).max(200).required(),
    english_name: Joi.string().trim().min(1).max(200).required(),
    status: Joi.string().valid('enabled', 'disabled').default('enabled'),
    auto_sku: Joi.string().valid('enabled', 'disabled').default('disabled'),
    category_level: Joi.number().integer().valid(1, 2, 3, 4, 5, 6, 7).required(),
    parent_id: Joi.number().integer().positive().optional().allow(null),
    category_description: Joi.string().trim().max(1000).optional().allow(''),
    attribute_tags: Joi.array().items(Joi.string().trim().min(1).max(50)).optional()
  }),

  // PUT /api/v1/categories/:id
  updateCategory: Joi.object({
    chinese_name: Joi.string().trim().min(1).max(200).optional(),
    english_name: Joi.string().trim().min(1).max(200).optional(),
    status: Joi.string().valid('enabled', 'disabled').optional(),
    auto_sku: Joi.string().valid('enabled', 'disabled').optional(),
    category_description: Joi.string().trim().max(1000).optional().allow(''),
    attribute_tags: Joi.array().items(Joi.string().trim().min(1).max(50)).optional()
  }),

  // PUT /api/v1/categories/:id/status
  updateStatus: Joi.object({
    status: Joi.string().valid('enabled', 'disabled').required()
  }),

  // POST /api/v1/categories/batch
  batchCreate: Joi.object({
    categories: Joi.array().items(
      Joi.object({
        category_code: Joi.string().trim().max(50).optional().allow('')
          .pattern(/^[A-Za-z0-9-_]*$/)
          .uppercase(),
        chinese_name: Joi.string().trim().min(1).max(200).required(),
        english_name: Joi.string().trim().min(1).max(200).required(),
        status: Joi.string().valid('enabled', 'disabled').default('enabled'),
        auto_sku: Joi.string().valid('enabled', 'disabled').default('disabled'),
        category_level: Joi.number().integer().valid(1, 2, 3, 4, 5, 6, 7).required(),
        parent_id: Joi.number().integer().positive().optional().allow(null),
        category_description: Joi.string().trim().max(1000).optional().allow(''),
        attribute_tags: Joi.array().items(Joi.string().trim().min(1).max(50)).optional()
      })
    ).min(1).max(50).required()
  }),

  // Common parameter validation
  categoryId: Joi.object({
    id: Joi.number().integer().positive().required()
  })
};
