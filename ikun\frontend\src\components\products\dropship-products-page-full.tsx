'use client'

import { useState, useEffect } from 'react'
import { AddProductCard, EditProductCard, ExcelImportCard } from './card'
import { BatchClaimCard } from './batch-claim-card'
import { useProducts } from '@/hooks/useProducts'
import { useCategoryTree } from '@/hooks/useCategories'
import { useConfirm } from '@/components/ui/confirm-dialog'
import { useToast } from '@/hooks/use-toast'
import { DropshipProduct, ProductCategory } from '@/types'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Plus,
  Upload,
  Edit,
  Copy,
  RefreshCw,
  ChevronDown,
  ChevronRight,
  Trash2,
  Search,
  RotateCcw,
  MoreHorizontal,
  Languages,
  FileText,
  Download,
  Filter,
  Calendar,
  FolderTree,
  Tag
} from 'lucide-react'

// 分类树节点接口（用于筛选）
interface CategoryNode {
  id: string
  name: string
  englishName: string
  children?: CategoryNode[]
}

// 将分类树转换为筛选用的格式
const convertCategoryTreeToFilterFormat = (categories: ProductCategory[]): CategoryNode[] => {
  return categories.map(cat => ({
    id: cat.id.toString(),
    name: cat.category__namecn,
    englishName: cat.category__name,
    children: cat.children ? convertCategoryTreeToFilterFormat(cat.children) : undefined
  }));
};

// 将API路径转换为格式化的中英文显示
const formatCategoryPath = (categoryPath: string, categoryTree: ProductCategory[]): string => {
  if (!categoryPath || !categoryTree) return categoryPath;

  const pathParts = categoryPath.split('/');
  const formattedParts: string[] = [];

  // 递归查找分类信息
  const findCategoryInfo = (categories: ProductCategory[], targetPath: string, currentPath: string = ''): ProductCategory | null => {
    for (const category of categories) {
      const fullPath = currentPath ? `${currentPath}/${category.category__name.toLowerCase()}` : category.category__name.toLowerCase();

      if (fullPath === targetPath) {
        return category;
      }

      if (category.children && targetPath.startsWith(fullPath + '/')) {
        return findCategoryInfo(category.children, targetPath, fullPath);
      }
    }
    return null;
  };

  // 逐级构建路径
  let currentPath = '';
  for (let i = 0; i < pathParts.length; i++) {
    currentPath = currentPath ? `${currentPath}/${pathParts[i]}` : pathParts[i];
    const categoryInfo = findCategoryInfo(categoryTree, currentPath);

    if (categoryInfo) {
      formattedParts.push(`${categoryInfo.category__namecn}（${categoryInfo.category__name}）`);
    } else {
      // 如果找不到对应的分类信息，使用原始路径
      formattedParts.push(pathParts[i]);
    }
  }

  return formattedParts.join(' - ');
};



// 筛选选项
const statusOptions = [
  { value: 'draft', label: '草稿' },
  { value: 'active', label: '活跃' },
  { value: 'inactive', label: '禁用' },
]

const sourceOptions = [
  { value: 'manual', label: '手动添加' },
  { value: 'excel', label: 'EXCEL导入' },
  { value: 'scraping', label: '采集箱' },
]



export function DropshipProductsPageFull() {
  const [selectedProducts, setSelectedProducts] = useState<number[]>([])
  const [searchValue, setSearchValue] = useState('')
  const { confirm } = useConfirm()
  const { toast } = useToast()

  // 筛选状态
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [selectedSources, setSelectedSources] = useState<string[]>([])
  const [dateRange, setDateRange] = useState<{start: string, end: string}>({
    start: '',
    end: ''
  })

  // 卡片弹窗状态
  const [showAddCard, setShowAddCard] = useState(false)
  const [showEditCard, setShowEditCard] = useState(false)
  const [showExcelImportCard, setShowExcelImportCard] = useState(false)
  const [showBatchClaimCard, setShowBatchClaimCard] = useState(false)
  const [editingProduct, setEditingProduct] = useState<DropshipProduct | null>(null)

  // 使用API hooks
  const {
    products,
    loading: productsLoading,
    error: productsError,
    pagination,
    fetchProducts,
    createProduct,
    updateProduct,
    deleteProduct
  } = useProducts();

  const {
    categoryTree: apiCategoryTree,
    loading: categoriesLoading,
    error: categoriesError
  } = useCategoryTree();

  // 转换分类树格式用于筛选
  const categoryTree = apiCategoryTree ? convertCategoryTreeToFilterFormat(apiCategoryTree) : [];

  // 筛选处理函数
  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    )
  }

  const handleStatusToggle = (status: string) => {
    setSelectedStatuses(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    )
  }

  const handleSourceToggle = (source: string) => {
    setSelectedSources(prev =>
      prev.includes(source)
        ? prev.filter(s => s !== source)
        : [...prev, source]
    )
  }

  const handleReset = () => {
    setSelectedCategories([])
    setSelectedStatuses([])
    setSelectedSources([])
    setDateRange({ start: '', end: '' })
    setSearchValue('')
    // 重新获取所有产品
    fetchProducts()
  }

  const handleBatchClaim = () => {
    if (selectedProducts.length === 0) {
      toast({
        title: '请选择产品',
        description: '请至少选择一个产品进行认领',
        variant: 'destructive'
      })
      return
    }
    setShowBatchClaimCard(true)
  }

  // 应用筛选
  const handleApplyFilters = () => {
    const params: any = {}

    if (searchValue) {
      params.search = searchValue
    }

    if (selectedStatuses.length > 0) {
      params.status = selectedStatuses.join(',')
    }

    if (selectedSources.length > 0) {
      params.source = selectedSources.join(',')
    }

    if (selectedCategories.length > 0) {
      params.category_id = selectedCategories.join(',')
    }

    if (dateRange.start) {
      params.start_date = dateRange.start
    }

    if (dateRange.end) {
      params.end_date = dateRange.end
    }

    fetchProducts(params)
  }

  // 监听筛选条件变化，自动应用筛选
  useEffect(() => {
    const timer = setTimeout(() => {
      handleApplyFilters()
    }, 500) // 防抖处理

    return () => clearTimeout(timer)
  }, [searchValue, selectedStatuses, selectedSources, selectedCategories, dateRange])

  // 产品操作处理函数
  const handleAddProduct = async (productData: Partial<DropshipProduct>) => {
    try {
      await createProduct(productData);
      setShowAddCard(false);
      toast({
        description: "产品已成功添加",
        variant: "default"
      });
    } catch (error) {
      console.error('创建产品失败:', error);
      toast({
        description: "添加产品时发生错误，请重试",
        variant: "destructive"
      });
    }
  }

  const handleEditProduct = async (updateData: any) => {
    if (!editingProduct) return;

    try {
      await updateProduct(editingProduct.id, updateData);
      setShowEditCard(false);
      setEditingProduct(null);
      toast({
        description: "产品信息已成功更新",
        variant: "default"
      });
    } catch (error) {
      console.error('更新产品失败:', error);
      toast({
        description: "更新产品信息时发生错误，请重试",
        variant: "destructive"
      });
    }
  }

  const handleDeleteProduct = async (productId: number) => {
    const confirmed = await confirm({
      title: '删除产品',
      description: '确定要删除这个产品吗？此操作不可撤销。',
      confirmText: '删除',
      cancelText: '取消',
      variant: 'destructive'
    });

    if (!confirmed) {
      return;
    }

    try {
      await deleteProduct(productId);
      setSelectedProducts(prev => prev.filter(id => id !== productId));
      toast({
        description: "产品已成功删除",
        variant: "default"
      });
    } catch (error: any) {
      console.error('删除产品失败:', error);
      console.log('Error response data:', error.response?.data);

      // 检查是否是产品被认领的错误
      if (error.response?.data?.error === 'PRODUCT_CLAIMED') {
        const claimedInfo = error.response.data.claimedInfo || [];
        console.log('Claimed info received:', claimedInfo);

        if (claimedInfo.length > 0) {
          const storeList = claimedInfo.map((info: any) =>
            `${info.platform_code}平台 - ${info.store_name}`
          ).join('、');

          await confirm({
            title: '无法删除产品',
            description: `该产品已被认领到以下店铺：\n\n${storeList}\n\n请先在产品上架页面取消认领，然后再删除产品。`,
            confirmText: '知道了',
            variant: 'info',
            icon: true
          });
        } else {
          await confirm({
            title: '无法删除产品',
            description: '该产品已被认领到店铺，无法删除。\n\n请先在产品上架页面取消认领，然后再删除产品。',
            confirmText: '知道了',
            variant: 'info',
            icon: true
          });
        }
        // 对于 PRODUCT_CLAIMED 错误，我们已经显示了友好提示，不需要再向上抛出错误
        return;
      } else {
        toast({
          description: "删除产品时发生错误，请重试",
          variant: "destructive"
        });
        // 对于其他类型的错误，我们也不向上抛出，避免页面跳转
        return;
      }
    }
  }

  const handleBatchDelete = async () => {
    if (selectedProducts.length === 0) {
      await confirm({
        title: '提示',
        description: '请先选择要删除的产品',
        confirmText: '知道了',
        variant: 'info',
        icon: true
      });
      return;
    }

    const confirmed = await confirm({
      title: '批量删除',
      description: `确定要删除选中的 ${selectedProducts.length} 个产品吗？此操作不可撤销。`,
      confirmText: `删除 ${selectedProducts.length} 个产品`,
      cancelText: '取消',
      variant: 'destructive'
    });

    if (!confirmed) {
      return;
    }

    // 批量删除处理
    const failedProducts: Array<{id: number, claimedInfo: any[]}> = [];
    let successCount = 0;

    for (const productId of selectedProducts) {
      try {
        await deleteProduct(productId);
        successCount++;
      } catch (error: any) {
        if (error.response?.data?.error === 'PRODUCT_CLAIMED') {
          failedProducts.push({
            id: productId,
            claimedInfo: error.response.data.claimedInfo || []
          });
        }
      }
    }

    // 更新选中状态
    setSelectedProducts([]);

    // 显示结果
    if (failedProducts.length > 0) {
      // 构建失败产品的提示信息
      //failedInfo精简显示，只显示SKU，一行1个
      const failedInfo = failedProducts.map(item => {
        // 查找对应的产品信息获取SKU
        const product = products.find(p => p.id === item.id);
        return product?.sku || `产品ID ${item.id}`;
      }).join('\n');

      //删除失败换行显示后面的信息
      await confirm({
        title: '批量删除完成',
        description: ` ${failedProducts.length} 个产品删除失败：\n\n${failedInfo}\n\n`,
        confirmText: '知道了',
        variant: 'info',
        icon: true
      });
    }

    // 成功删除的toast提示（如果有成功的）
    if (successCount > 0) {
      toast({
        description: `已成功删除 ${successCount} 个产品`,
        variant: "default"
      });
    }
  }

  // 处理批量导出
  const handleBatchExport = async () => {
    if (selectedProducts.length === 0) {
      toast({
        title: "请选择产品",
        description: "请先选择要导出的产品",
        variant: "destructive"
      })
      return
    }

    try {
      // 获取认证token
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      // 构建查询参数
      const params = new URLSearchParams()
      params.append('export_all', 'false')

      selectedProducts.forEach(id => {
        params.append('selected_ids', id.toString())
      })

      const response = await fetch(`/api/v1/products/export?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('导出失败')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `铺货产品数据_选中${selectedProducts.length}个_${new Date().toISOString().split('T')[0]}.xlsx`
      a.click()
      window.URL.revokeObjectURL(url)

      toast({
        title: "导出成功",
        description: `已导出选中的 ${selectedProducts.length} 个产品`,
        variant: "default"
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: "导出数据时发生错误，请重试",
        variant: "destructive"
      })
    }
  }

  const handleOpenEdit = (product: DropshipProduct) => {
    setEditingProduct(product)
    setShowEditCard(true)
  }

  const getStatusBadge = (status: string) => {
    const statusMap = {
      draft: { label: '草稿', className: 'bg-orange-100 text-orange-800' },
      active: { label: '活跃', className: 'bg-green-100 text-green-800' },
      inactive: { label: '禁用', className: 'bg-gray-100 text-gray-800' }
    }
    const config = statusMap[status as keyof typeof statusMap] || statusMap.draft
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.className}`}>
        {config.label}
      </span>
    )
  }



  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(products.map(p => p.id))
    } else {
      setSelectedProducts([])
    }
  }

  const handleSelectProduct = (productId: number, checked: boolean) => {
    if (checked) {
      setSelectedProducts([...selectedProducts, productId])
    } else {
      setSelectedProducts(selectedProducts.filter(id => id !== productId))
    }
  }

  // 处理产品行单击选中
  const handleProductClick = (productId: number, event: React.MouseEvent) => {
    // 防止复选框点击触发行点击
    if ((event.target as HTMLElement).closest('input[type="checkbox"]') ||
        (event.target as HTMLElement).closest('button')) {
      return
    }

    const isSelected = selectedProducts.includes(productId)
    handleSelectProduct(productId, !isSelected)
  }

  // 处理产品行双击编辑
  const handleProductDoubleClick = (product: DropshipProduct) => {
    handleOpenEdit(product)
  }

  // 显示加载状态
  if (productsLoading && products.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    );
  }

  // 显示错误状态
  if (productsError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">加载失败: {productsError}</p>
          <Button onClick={() => fetchProducts()} variant="outline">
            重试
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-[calc(100vh-4rem)]">
      <Card className="flex-1 flex flex-col">
        <CardContent className="p-0 flex-1 flex flex-col">
          {/* 工具栏 */}
          <div className="border-b p-4">
          {/* 主要操作按钮 */}
          <div className="flex flex-wrap gap-2 mb-4">
          <Button size="sm" variant="outline">
              <Download className="w-4 h-4 mr-1" />
              从采集箱认领
            </Button>
            <Button size="sm" variant="outline" onClick={() => setShowAddCard(true)}>
              <Plus className="w-4 h-4 mr-1" />
              手动添加
            </Button>
            <Button size="sm" variant="outline" onClick={() => setShowExcelImportCard(true)}>
              <Download className="w-4 h-4 mr-1" />
              Excel导入
            </Button>

            <Button size="sm" variant="outline" onClick={handleBatchExport}>
              <Download className="w-4 h-4 mr-1" />
              批量导出
            </Button>

            {/* 批量操作下拉菜单 */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" variant="outline">
                  批量操作
                  <ChevronDown className="w-4 h-4 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem>
                  <Edit className="w-4 h-4 mr-2" />
                  批量修改
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleBatchClaim}
                  disabled={selectedProducts.length === 0}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  批量认领 {selectedProducts.length > 0 && `(${selectedProducts.length})`}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleBatchDelete}>
                  <FileText className="w-4 h-4 mr-2" />
                  批量删除
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              size="sm"
              variant="outline"
              onClick={handleBatchDelete}
              disabled={selectedProducts.length === 0}
            >
              <Trash2 className="w-4 h-4 mr-1" />
              删除 {selectedProducts.length > 0 && `(${selectedProducts.length})`}
            </Button>
          </div>

          {/* 筛选和搜索区域 */}
          <div className="pt-4 border-t">
            <div className="flex items-center justify-between gap-4">
              {/* 左侧：筛选下拉菜单 */}
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium text-muted-foreground">全部</span>

                {/* 产品类目筛选 */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="w-32 justify-start">
                      <FolderTree className="w-4 h-4 mr-1 flex-shrink-0" />
                      <span className="truncate">产品类目</span>
                      {selectedCategories.length > 0 && (
                        <span className="ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0">
                          {selectedCategories.length}
                        </span>
                      )}
                      <ChevronDown className="w-4 h-4 ml-auto flex-shrink-0" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-64 max-h-80 overflow-y-auto">
                    <CategoryTreeSelector
                      categories={categoryTree}
                      selectedCategories={selectedCategories}
                      onToggle={handleCategoryToggle}
                    />
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* 产品状态筛选 */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="w-32 justify-start">
                      <Tag className="w-4 h-4 mr-1 flex-shrink-0" />
                      <span className="truncate">产品状态</span>
                      {selectedStatuses.length > 0 && (
                        <span className="ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0">
                          {selectedStatuses.length}
                        </span>
                      )}
                      <ChevronDown className="w-4 h-4 ml-auto flex-shrink-0" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    {statusOptions.map((status) => (
                      <DropdownMenuCheckboxItem
                        key={status.value}
                        checked={selectedStatuses.includes(status.value)}
                        onCheckedChange={() => handleStatusToggle(status.value)}
                      >
                        {status.label}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* 产品来源筛选 */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="w-32 justify-start">
                      <Download className="w-4 h-4 mr-1 flex-shrink-0" />
                      <span className="truncate">产品来源</span>
                      {selectedSources.length > 0 && (
                        <span className="ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0">
                          {selectedSources.length}
                        </span>
                      )}
                      <ChevronDown className="w-4 h-4 ml-auto flex-shrink-0" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    {sourceOptions.map((source) => (
                      <DropdownMenuCheckboxItem
                        key={source.value}
                        checked={selectedSources.includes(source.value)}
                        onCheckedChange={() => handleSourceToggle(source.value)}
                      >
                        {source.label}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* 创建时间筛选 */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="w-32 justify-start">
                      <Calendar className="w-4 h-4 mr-1 flex-shrink-0" />
                      <span className="truncate">创建时间</span>
                      {(dateRange.start || dateRange.end) && (
                        <span className="ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 flex-shrink-0">
                          1
                        </span>
                      )}
                      <ChevronDown className="w-4 h-4 ml-auto flex-shrink-0" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-64 p-3">
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium">开始日期</label>
                        <Input
                          type="date"
                          value={dateRange.start}
                          onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium">结束日期</label>
                        <Input
                          type="date"
                          value={dateRange.end}
                          onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* 右侧：搜索区域 */}
              <div className="flex items-center gap-2">
                <Input
                  placeholder="搜索产品名称、SKU或ID..."
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  className="w-64"
                />
                <Button size="sm">
                  <Search className="w-4 h-4 mr-1" />
                  搜索
                </Button>
                <Button size="sm" variant="outline" onClick={handleReset}>
                  <RotateCcw className="w-4 h-4 mr-1" />
                  重置
                </Button>
              </div>
            </div>
          </div>
        </div>

          {/* 表格区域 */}
          <div className="flex-1 overflow-x-auto">
            <table className="w-full text-sm border-separate border-spacing-0 table-fixed">
              <colgroup>
                <col className="w-12" />
                <col className="w-16" />
                <col className="w-[35%]" />
                <col className="w-20" />
                <col className="w-[10%]" />
                <col className="w-24" />
                <col className="w-28" />
                <col className="w-24" />
                <col className="w-32" />
                <col className="w-16" />
              </colgroup>
              <thead>
                <tr className="bg-muted/30 border-b h-14">
                  <th className="p-3 text-left border-r border-border/50 h-14">
                    <div className="flex items-center justify-center h-full">
                      <Checkbox
                        checked={selectedProducts.length === products.length}
                        onCheckedChange={handleSelectAll}
                      />
                    </div>
                  </th>
                  <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                    <div className="flex items-center justify-center h-full">图片</div>
                  </th>
                  <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                    <div className="flex items-center justify-center h-full">产品信息</div>
                  </th>
                  <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                    <div className="flex items-center justify-center h-full">状态</div>
                  </th>
                  <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                    <div className="flex items-center justify-center h-full">SKU/EAN</div>
                  </th>
                  <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                    <div className="flex items-center justify-center h-full">价格/重量</div>
                  </th>
                  <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                    <div className="flex items-center justify-center h-full">包装信息CM</div>
                  </th>
                  <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                    <div className="flex items-center justify-center h-full">认领/刊登</div>
                  </th>
                  <th className="p-3 text-left font-medium text-muted-foreground border-r border-border/50 text-center h-14">
                    <div className="flex items-center justify-center h-full">创建时间</div>
                  </th>
                  <th className="p-3 text-left font-medium text-muted-foreground h-14">
                    <div className="flex items-center justify-center h-full">操作</div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {products.map((product, index) => (
                  <tr
                    key={product.id}
                    className={`border-b border-border/50 hover:bg-muted/30 transition-colors cursor-pointer h-16 ${
                      index % 2 === 0 ? 'bg-background' : 'bg-muted/10'
                    } ${selectedProducts.includes(product.id) ? 'bg-blue-50 border-blue-200' : ''}`}
                    onClick={(e) => handleProductClick(product.id, e)}
                    onDoubleClick={() => handleProductDoubleClick(product)}
                  >
                    <td className="p-3 border-r border-border/50">
                      <Checkbox
                        checked={selectedProducts.includes(product.id)}
                        onCheckedChange={(checked) =>
                          handleSelectProduct(product.id, checked as boolean)
                        }
                      />
                    </td>
                    <td className="p-3 border-r border-border/50">
                      <ImageHover
                        src={product.image1}
                        alt={product.english_title}
                        className="w-12 h-12 object-cover rounded-lg border shadow-sm cursor-pointer"
                      />
                    </td>
                    <td className="p-3 border-r border-border/50">
                      <div className="space-y-1">
                        <div className="font-medium text-foreground leading-tight line-clamp-1" title={product.english_title}>
                          {product.english_title}
                        </div>
                        <div className="text-xs text-muted-foreground line-clamp-1">
                          ID: {product.id} | 来源: {product.source}
                        </div>
                        <div className="text-muted-foreground text-xs line-clamp-1">
                          {formatCategoryPath(product.category, apiCategoryTree || [])}
                        </div>
                      </div>
                    </td>
                    <td className="p-3 border-r border-border/50 text-center">
                      {getStatusBadge(product.status)}
                    </td>
                    <td className="p-3 border-r border-border/50 text-center">
                      <div className="text-sm space-y-1">
                        <div className="font-medium text-foreground leading-tight line-clamp-1">{product.sku}</div>
                        <div className="font-medium text-foreground leading-tight line-clamp-1">{product.ean}</div>
                      </div>
                    </td>
                    <td className="p-3 border-r border-border/50 text-center">
                      <div className="text-sm space-y-1">
                        <div className="font-semibold text-green-600">¥{product.cost_price || 0}</div>
                        <div className="text-muted-foreground text-xs">{product.package_weight || 0}g</div>
                      </div>
                    </td>
                    <td className="p-3 border-r border-border/50">
                      <div className="text-xs text-muted-foreground text-center">
                      <div>{`${Math.floor(product.package_length || 0)}×${Math.floor(product.package_width || 0)}×${Math.floor(product.package_height || 0)}`}</div>                      </div>
                    </td>
                    <td className="p-3 border-r border-border/50">
                      <div className="text-sm space-y-1">
                        {product.claim_platform && (
                          <div className="text-blue-600 font-medium text-xs">
                            {product.claim_platform}
                          </div>
                        )}
                        <div className="text-muted-foreground text-xs">
                          刊登: {product.listing_count}次
                        </div>
                      </div>
                    </td>
                    <td className="p-3 border-r border-border/50">
                      <div className="text-xs text-muted-foreground">
                        {new Date(product.created_at).toLocaleString('zh-CN')}
                      </div>
                    </td>
                    <td className="p-3">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleOpenEdit(product)}>
                            <Edit className="w-4 h-4 mr-2" />
                            编辑
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Copy className="w-4 h-4 mr-2" />
                            复制
                          </DropdownMenuItem>
                          <DropdownMenuItem>认领</DropdownMenuItem>
                          <DropdownMenuItem>查看详情</DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDeleteProduct(product.id)}
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                ))}
              </tbody>
          </table>
          </div>

          {/* 固定底部分页区域 */}
          <div className="flex items-center justify-between px-6 py-2 border-t bg-background/95 backdrop-blur-sm mt-auto">
            <div className="text-sm text-muted-foreground">
              显示 {((pagination.page - 1) * pagination.limit) + 1}-{Math.min(pagination.page * pagination.limit, pagination.total)} 条，共 {pagination.total} 条记录
            </div>
            <div className="flex items-center space-x-1">
              <Button
                size="sm"
                variant="outline"
                disabled={pagination.page <= 1}
                onClick={() => fetchProducts({ page: pagination.page - 1 })}
                className="h-8 px-3 text-xs"
              >
                上一页
              </Button>

              {/* 页码按钮 */}
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const pageNum = Math.max(1, pagination.page - 2) + i;
                if (pageNum > pagination.totalPages) return null;

                return (
                  <Button
                    key={pageNum}
                    size="sm"
                    variant={pageNum === pagination.page ? "default" : "outline"}
                    onClick={() => fetchProducts({ page: pageNum })}
                    className="h-8 px-3 text-xs"
                  >
                    {pageNum}
                  </Button>
                );
              })}

              <Button
                size="sm"
                variant="outline"
                disabled={pagination.page >= pagination.totalPages}
                onClick={() => fetchProducts({ page: pagination.page + 1 })}
                className="h-8 px-3 text-xs"
              >
                下一页
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 添加产品卡片 */}
      <AddProductCard
        open={showAddCard}
        onOpenChange={setShowAddCard}
        onSave={handleAddProduct}
      />

      {/* 编辑产品卡片 */}
      <EditProductCard
        open={showEditCard}
        onOpenChange={setShowEditCard}
        product={editingProduct}
        onSave={handleEditProduct}
      />

      {/* Excel 导入/导出卡片 */}
      <ExcelImportCard
        open={showExcelImportCard}
        onOpenChange={setShowExcelImportCard}
        onImportComplete={() => {
          // 导入完成后刷新产品列表
          fetchProducts()
        }}
      />

      {/* 批量认领卡片 */}
      <BatchClaimCard
        open={showBatchClaimCard}
        onOpenChange={setShowBatchClaimCard}
        selectedProductIds={selectedProducts}
        onSuccess={() => {
          // 认领完成后刷新产品列表
          fetchProducts()
          setSelectedProducts([])
        }}
      />
    </div>
  )
}

// 产品类目树状选择器组件
function CategoryTreeSelector({
  categories,
  selectedCategories,
  onToggle
}: {
  categories: CategoryNode[]
  selectedCategories: string[]
  onToggle: (categoryId: string) => void
}) {
  const [expandedNodes, setExpandedNodes] = useState<string[]>(['1', '2', '3'])

  const toggleExpanded = (nodeId: string) => {
    setExpandedNodes(prev =>
      prev.includes(nodeId)
        ? prev.filter(id => id !== nodeId)
        : [...prev, nodeId]
    )
  }

  const renderCategoryNode = (category: CategoryNode, level: number = 0) => {
    const hasChildren = category.children && category.children.length > 0
    const isExpanded = expandedNodes.includes(category.id)
    const isSelected = selectedCategories.includes(category.id)

    return (
      <div key={category.id}>
        <div
          className="flex items-center gap-2 py-1 px-2 hover:bg-muted/50 rounded"
          style={{ paddingLeft: `${level * 16 + 8}px` }}
        >
          {hasChildren ? (
            <button
              onClick={() => toggleExpanded(category.id)}
              className="p-0.5 hover:bg-muted rounded"
            >
              {isExpanded ? (
                <ChevronDown className="w-3 h-3" />
              ) : (
                <ChevronRight className="w-3 h-3" />
              )}
            </button>
          ) : (
            <div className="w-4" />
          )}

          <Checkbox
            checked={isSelected}
            onCheckedChange={() => onToggle(category.id)}
            className="h-4 w-4"
          />

          <span className="text-sm flex-1">{category.name}</span>
        </div>

        {hasChildren && isExpanded && (
          <div>
            {category.children?.map((child) => renderCategoryNode(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-1">
      {categories.map((category) => renderCategoryNode(category))}
    </div>
  )
}

// 图片悬停预览组件
function ImageHover({
  src,
  alt,
  className
}: {
  src: string
  alt: string
  className?: string
}) {
  const [showPreview, setShowPreview] = useState(false)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  const handleMouseEnter = (e: React.MouseEvent) => {
    setShowPreview(true)
    setMousePosition({ x: e.clientX, y: e.clientY })
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    setMousePosition({ x: e.clientX, y: e.clientY })
  }

  const handleMouseLeave = () => {
    setShowPreview(false)
  }

  return (
    <>
      <img
        src={src}
        alt={alt}
        className={className}
        onMouseEnter={handleMouseEnter}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
      />

      {showPreview && (
        <div
          className="fixed z-50 pointer-events-none"
          style={{
            left: mousePosition.x + 15,
            top: mousePosition.y - 100,
          }}
        >
          <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-2">
            <img
              src={src}
              alt={alt}
              className="w-64 h-64 object-cover rounded"
            />
          </div>
        </div>
      )}
    </>
  )
}
