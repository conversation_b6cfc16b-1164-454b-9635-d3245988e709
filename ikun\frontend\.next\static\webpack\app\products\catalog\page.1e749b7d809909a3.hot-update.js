"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/catalog/page",{

/***/ "(app-pages-browser)/./src/components/products/product-catalog-page.tsx":
/*!**********************************************************!*\
  !*** ./src/components/products/product-catalog-page.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductCatalogPage: function() { return /* binding */ ProductCatalogPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/confirm-dialog */ \"(app-pages-browser)/./src/components/ui/confirm-dialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/expand.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Expand,Minimize2,Plus,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_useCategories__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useCategories */ \"(app-pages-browser)/./src/hooks/useCategories.ts\");\n/* __next_internal_client_entry_do_not_use__ ProductCatalogPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductCatalogPage() {\n    _s();\n    const [expandedNodes, setExpandedNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"1\",\n        \"2\"\n    ]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedNodeLevel, setSelectedNodeLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 存储选中节点的层级\n    ;\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdding, setIsAdding] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { confirm } = (0,_components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_10__.useConfirm)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    // 使用API hooks\n    const { categoryTree: apiCategoryTree, loading: categoriesLoading, error: categoriesError, refetch: refetchCategories } = (0,_hooks_useCategories__WEBPACK_IMPORTED_MODULE_12__.useCategoryTree)();\n    const { createCategory, updateCategory, deleteCategory, loading: operationLoading } = (0,_hooks_useCategories__WEBPACK_IMPORTED_MODULE_12__.useCategories)();\n    // 转换API数据为显示格式\n    const convertCategoryToNode = (category)=>({\n            id: category.id.toString(),\n            name: category.chinese_name,\n            englishName: category.english_name,\n            categoryCode: category.category_code || \"\",\n            description: category.category_description || \"\",\n            tags: category.attribute_tags || [],\n            createdAt: new Date(category.created_at).toLocaleString(\"zh-CN\"),\n            status: category.status === \"enabled\" ? \"active\" : \"inactive\",\n            children: category.children ? category.children.map(convertCategoryToNode) : undefined\n        });\n    const catalogTree = apiCategoryTree ? apiCategoryTree.map(convertCategoryToNode) : [];\n    const toggleExpanded = (nodeId)=>{\n        setExpandedNodes((prev)=>prev.includes(nodeId) ? prev.filter((id)=>id !== nodeId) : [\n                ...prev,\n                nodeId\n            ]);\n    };\n    // 递归获取所有有子节点的节点ID\n    const getAllExpandableNodeIds = (nodes)=>{\n        const ids = [];\n        const traverse = (nodeList)=>{\n            nodeList.forEach((node)=>{\n                if (node.children && node.children.length > 0) {\n                    ids.push(node.id);\n                    traverse(node.children);\n                }\n            });\n        };\n        traverse(nodes);\n        return ids;\n    };\n    // 展开全部\n    const handleExpandAll = ()=>{\n        const allExpandableIds = getAllExpandableNodeIds(catalogTree);\n        setExpandedNodes(allExpandableIds);\n    };\n    // 折叠全部\n    const handleCollapseAll = ()=>{\n        setExpandedNodes([]);\n    };\n    // 递归搜索匹配的节点\n    const searchNodes = (nodes, searchTerm)=>{\n        if (!searchTerm.trim()) return nodes;\n        const results = [];\n        const lowerSearchTerm = searchTerm.toLowerCase();\n        const searchInNode = (node)=>{\n            var _node_categoryCode;\n            // 检查当前节点是否匹配\n            const nameMatch = node.name.toLowerCase().includes(lowerSearchTerm);\n            const englishNameMatch = node.englishName.toLowerCase().includes(lowerSearchTerm);\n            const codeMatch = (_node_categoryCode = node.categoryCode) === null || _node_categoryCode === void 0 ? void 0 : _node_categoryCode.toLowerCase().includes(lowerSearchTerm);\n            const isCurrentMatch = nameMatch || englishNameMatch || codeMatch;\n            // 递归搜索子节点\n            const matchingChildren = [];\n            if (node.children) {\n                node.children.forEach((child)=>{\n                    const childResult = searchInNode(child);\n                    if (childResult) {\n                        matchingChildren.push(childResult);\n                    }\n                });\n            }\n            // 如果当前节点匹配或有匹配的子节点，则返回节点\n            if (isCurrentMatch || matchingChildren.length > 0) {\n                return {\n                    ...node,\n                    children: matchingChildren.length > 0 ? matchingChildren : node.children\n                };\n            }\n            return null;\n        };\n        nodes.forEach((node)=>{\n            const result = searchInNode(node);\n            if (result) {\n                results.push(result);\n            }\n        });\n        return results;\n    };\n    // 获取搜索结果中所有匹配节点的路径（用于自动展开）\n    const getMatchingNodePaths = (nodes, searchTerm)=>{\n        if (!searchTerm.trim()) return [];\n        const paths = [];\n        const lowerSearchTerm = searchTerm.toLowerCase();\n        const findPaths = function(nodeList) {\n            let currentPath = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n            nodeList.forEach((node)=>{\n                var _node_categoryCode;\n                const newPath = [\n                    ...currentPath,\n                    node.id\n                ];\n                // 检查当前节点是否匹配\n                const nameMatch = node.name.toLowerCase().includes(lowerSearchTerm);\n                const englishNameMatch = node.englishName.toLowerCase().includes(lowerSearchTerm);\n                const codeMatch = (_node_categoryCode = node.categoryCode) === null || _node_categoryCode === void 0 ? void 0 : _node_categoryCode.toLowerCase().includes(lowerSearchTerm);\n                if (nameMatch || englishNameMatch || codeMatch) {\n                    // 添加到匹配节点的所有父节点路径\n                    newPath.slice(0, -1).forEach((parentId)=>{\n                        if (!paths.includes(parentId)) {\n                            paths.push(parentId);\n                        }\n                    });\n                }\n                // 递归搜索子节点\n                if (node.children && node.children.length > 0) {\n                    findPaths(node.children, newPath);\n                }\n            });\n        };\n        findPaths(nodes);\n        return paths;\n    };\n    // 处理搜索\n    const handleSearch = ()=>{\n        if (!searchValue.trim()) {\n            // 如果搜索框为空，重置展开状态\n            setExpandedNodes([\n                \"1\",\n                \"2\"\n            ]) // 恢复默认展开状态\n            ;\n            return;\n        }\n        // 获取匹配节点的父节点路径并自动展开\n        const pathsToExpand = getMatchingNodePaths(catalogTree, searchValue);\n        setExpandedNodes(pathsToExpand);\n    };\n    // 重置搜索\n    const handleResetSearch = ()=>{\n        setSearchValue(\"\");\n    };\n    // 过滤后的目录树（用于显示搜索结果）\n    const filteredCatalogTree = searchValue.trim() ? searchNodes(catalogTree, searchValue) : catalogTree;\n    const handleAdd = ()=>{\n        setIsAdding(true);\n        setIsEditing(false);\n        setSelectedNode(null);\n        setSelectedNodeLevel(0) // 一级分类的层级为0\n        ;\n        setShowEditDialog(true);\n        setFormData({\n            name: \"\",\n            englishName: \"\",\n            categoryCode: \"\",\n            description: \"\",\n            tags: [],\n            status: \"active\"\n        });\n    };\n    const handleEditNode = (node)=>{\n        setSelectedNode(node);\n        setIsEditing(true);\n        setIsAdding(false);\n        setShowEditDialog(true);\n        setFormData({\n            name: node.name,\n            englishName: node.englishName,\n            categoryCode: node.categoryCode,\n            description: node.description,\n            tags: node.tags,\n            status: node.status\n        });\n    };\n    const handleDeleteNode = async (node)=>{\n        const confirmed = await confirm({\n            title: \"删除分类\",\n            description: '确定要删除分类\"'.concat(node.name, '\"吗？此操作不可撤销。'),\n            confirmText: \"删除\",\n            cancelText: \"取消\",\n            variant: \"destructive\"\n        });\n        if (confirmed) {\n            try {\n                await deleteCategory(parseInt(node.id));\n                refetchCategories();\n                // 显示删除成功提示\n                toast({\n                    title: \"删除成功\"\n                });\n            } catch (error) {\n                console.error(\"删除失败:\", error);\n                // 显示删除失败提示\n                toast({\n                    title: \"删除失败\",\n                    description: error instanceof Error ? error.message : \"删除时发生未知错误\",\n                    variant: \"destructive\"\n                });\n            }\n        }\n    };\n    const handleAddChild = (parentNode, parentLevel)=>{\n        setIsAdding(true);\n        setIsEditing(false);\n        setSelectedNode(parentNode);\n        setSelectedNodeLevel(parentLevel) // 存储父节点的层级\n        ;\n        setShowEditDialog(true);\n        setFormData({\n            name: \"\",\n            englishName: \"\",\n            categoryCode: \"\",\n            description: \"\",\n            tags: [],\n            status: \"active\"\n        });\n    };\n    const handleSave = async ()=>{\n        try {\n            var _formData_name, _formData_englishName;\n            // 验证必填字段\n            if (!((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim())) {\n                toast({\n                    title: \"验证失败\",\n                    description: \"请输入中文名称\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (!((_formData_englishName = formData.englishName) === null || _formData_englishName === void 0 ? void 0 : _formData_englishName.trim())) {\n                toast({\n                    title: \"验证失败\",\n                    description: \"请输入英文名称\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (isAdding) {\n                var _formData_categoryCode, _formData_description;\n                // 创建新分类\n                const categoryCode = (_formData_categoryCode = formData.categoryCode) === null || _formData_categoryCode === void 0 ? void 0 : _formData_categoryCode.trim();\n                // 根据是否有选中的父节点来确定层级和父ID\n                let categoryLevel = 1;\n                let parentId = null;\n                if (selectedNode) {\n                    // 如果有选中的父节点，说明是添加子分类\n                    parentId = parseInt(selectedNode.id);\n                    // 根据父节点的层级确定子节点层级\n                    // selectedNodeLevel: 0=一级, 1=二级, 2=三级, 3=四级, 4=五级, 5=六级 (前端显示层级)\n                    // category_level: 1=一级, 2=二级, 3=三级, 4=四级, 5=五级, 6=六级, 7=七级 (后端数据库层级)\n                    // 子分类的层级 = 父分类的数据库层级 + 1 = (selectedNodeLevel + 1) + 1\n                    categoryLevel = selectedNodeLevel + 2;\n                }\n                const createData = {\n                    // 分类编码处理：如果有值则转换为大写，如果为空则不传递该字段\n                    ...categoryCode ? {\n                        category_code: categoryCode.toUpperCase()\n                    } : {},\n                    chinese_name: formData.name.trim(),\n                    english_name: formData.englishName.trim(),\n                    status: formData.status === \"active\" ? \"enabled\" : \"disabled\",\n                    category_level: categoryLevel,\n                    parent_id: parentId,\n                    category_description: ((_formData_description = formData.description) === null || _formData_description === void 0 ? void 0 : _formData_description.trim()) || \"\",\n                    attribute_tags: formData.tags || []\n                };\n                await createCategory(createData);\n                setIsAdding(false);\n                setShowEditDialog(false);\n                setFormData({}) // 清空表单数据\n                ;\n                await refetchCategories() // 等待刷新完成\n                ;\n                // 显示成功提示\n                toast({\n                    title: \"创建成功\"\n                });\n            } else if (selectedNode && isEditing) {\n                var _formData_description1;\n                // 更新分类 - 编辑时不允许修改分类编码\n                const updateData = {\n                    chinese_name: formData.name.trim(),\n                    english_name: formData.englishName.trim(),\n                    status: formData.status === \"active\" ? \"enabled\" : \"disabled\",\n                    category_description: ((_formData_description1 = formData.description) === null || _formData_description1 === void 0 ? void 0 : _formData_description1.trim()) || \"\",\n                    attribute_tags: formData.tags || []\n                };\n                await updateCategory(parseInt(selectedNode.id), updateData);\n                setIsEditing(false);\n                setShowEditDialog(false);\n                await refetchCategories() // 等待刷新完成\n                ;\n                // 显示成功提示\n                toast({\n                    title: \"更新成功\"\n                });\n            }\n        } catch (error) {\n            console.error(\"保存失败:\", error);\n            // 显示错误提示\n            toast({\n                title: \"保存失败\",\n                description: error instanceof Error ? error.message : \"保存时发生未知错误\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleCancel = ()=>{\n        setIsEditing(false);\n        setIsAdding(false);\n        setShowEditDialog(false);\n        setFormData({});\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            placeholder: \"搜索目录\",\n                                            value: searchValue,\n                                            onChange: (e)=>{\n                                                setSearchValue(e.target.value);\n                                                // 实时搜索：输入时自动触发搜索\n                                                if (e.target.value.trim()) {\n                                                    const pathsToExpand = getMatchingNodePaths(catalogTree, e.target.value);\n                                                    setExpandedNodes(pathsToExpand);\n                                                } else {\n                                                    setExpandedNodes([\n                                                        \"1\",\n                                                        \"2\"\n                                                    ]) // 恢复默认展开状态\n                                                    ;\n                                                }\n                                            },\n                                            onKeyDown: (e)=>{\n                                                if (e.key === \"Enter\") {\n                                                    handleSearch();\n                                                }\n                                            },\n                                            className: \"w-80\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: handleResetSearch,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"重置\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleExpandAll,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"展开全部\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleCollapseAll,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"折叠全部\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        onClick: handleAdd,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"新增一级产品目录\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0\",\n                    children: categoriesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-muted-foreground\",\n                                children: \"加载中...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 13\n                    }, this) : categoriesError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm mb-2\",\n                                children: [\n                                    \"加载失败: \",\n                                    categoriesError\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: refetchCategories,\n                                children: \"重试\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm border-separate border-spacing-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"bg-muted/30 border-b\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"名称 / 英文名\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-32 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"描述\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-20 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"SKU数量\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"分类编码\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"报关编码\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"属性栏目\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-32 p-4 text-left font-medium text-muted-foreground border-r border-border/50\",\n                                                children: \"更新时间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"w-24 p-4 text-left font-medium text-muted-foreground\",\n                                                children: \"操作\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: filteredCatalogTree.length > 0 ? filteredCatalogTree.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableTreeNode, {\n                                            node: node,\n                                            level: 0,\n                                            expandedNodes: expandedNodes,\n                                            onToggleExpanded: toggleExpanded,\n                                            onEdit: handleEditNode,\n                                            onDelete: handleDeleteNode,\n                                            onAddChild: handleAddChild,\n                                            searchTerm: searchValue\n                                        }, node.id, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 23\n                                        }, this)) : searchValue.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: 8,\n                                            className: \"p-8 text-center text-muted-foreground\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-8 h-8 text-muted-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"未找到匹配的目录\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: \"请尝试其他关键词或检查拼写\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: 8,\n                                            className: \"p-8 text-center text-muted-foreground\",\n                                            children: \"暂无数据\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: showEditDialog,\n                onOpenChange: setShowEditDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"max-w-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                children: isAdding ? selectedNode ? \"添加\".concat([\n                                    \"二\",\n                                    \"三\",\n                                    \"四\",\n                                    \"五\",\n                                    \"六\",\n                                    \"七\"\n                                ][selectedNodeLevel], \"级产品目录\") : \"添加一级产品目录\" : \"编辑产品目录\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CatalogForm, {\n                            formData: formData,\n                            setFormData: setFormData,\n                            isEditing: true,\n                            isEditingExisting: !isAdding,\n                            onSave: handleSave,\n                            onCancel: handleCancel,\n                            loading: operationLoading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 534,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 533,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n        lineNumber: 412,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCatalogPage, \"90os8Ia1Am+c2vsT027XTEd1NRM=\", false, function() {\n    return [\n        _components_ui_confirm_dialog__WEBPACK_IMPORTED_MODULE_10__.useConfirm,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast,\n        _hooks_useCategories__WEBPACK_IMPORTED_MODULE_12__.useCategoryTree,\n        _hooks_useCategories__WEBPACK_IMPORTED_MODULE_12__.useCategories\n    ];\n});\n_c = ProductCatalogPage;\n// 表格树节点组件\nfunction TableTreeNode(param) {\n    let { node, level, expandedNodes, onToggleExpanded, onEdit, onDelete, onAddChild, searchTerm = \"\" } = param;\n    var _node_children;\n    const hasChildren = node.children && node.children.length > 0;\n    const isExpanded = expandedNodes.includes(node.id);\n    // 高亮显示匹配的文本\n    const highlightText = (text, searchTerm)=>{\n        if (!searchTerm.trim()) return text;\n        const regex = new RegExp(\"(\".concat(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \")\"), \"gi\");\n        const parts = text.split(regex);\n        return parts.map((part, index)=>regex.test(part) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n                className: \"bg-yellow-200 text-yellow-900 px-1 rounded\",\n                children: part\n            }, index, false, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 593,\n                columnNumber: 9\n            }, this) : part);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                className: \"border-b border-border/50 hover:bg-muted/30 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            style: {\n                                paddingLeft: \"\".concat(level * 20, \"px\")\n                            },\n                            children: [\n                                hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>onToggleExpanded(node.id),\n                                    className: \"p-0.5 hover:bg-muted rounded\",\n                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Expand_Minimize2_Plus_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-foreground\",\n                                            children: highlightText(node.name, searchTerm)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: highlightText(node.englishName, searchTerm)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 603,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground truncate max-w-32\",\n                            title: node.description,\n                            children: node.description || \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 635,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 634,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                            variant: \"outline\",\n                            className: \"text-xs\",\n                            children: node.categoryCode ? highlightText(node.categoryCode, searchTerm) : \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"--\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 643,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-1\",\n                            children: node.tags.length > 0 ? node.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"text-xs\",\n                                    children: tag\n                                }, index, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 17\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"--\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4 border-r border-border/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: node.createdAt\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 659,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onEdit(node),\n                                    className: \"h-7 px-2 text-xs text-blue-600 hover:text-blue-800\",\n                                    children: \"编辑\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 13\n                                }, this),\n                                level < 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onAddChild(node, level),\n                                    className: \"h-7 px-2 text-xs text-green-600 hover:text-green-800\",\n                                    children: [\n                                        \"新增\",\n                                        [\n                                            \"二\",\n                                            \"三\",\n                                            \"四\",\n                                            \"五\",\n                                            \"六\",\n                                            \"七\"\n                                        ][level],\n                                        \"级目录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 674,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>onDelete(node),\n                                    className: \"h-7 px-2 text-xs text-red-600 hover:text-red-800\",\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 683,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 664,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 663,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                lineNumber: 602,\n                columnNumber: 7\n            }, this),\n            hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: (_node_children = node.children) === null || _node_children === void 0 ? void 0 : _node_children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableTreeNode, {\n                        node: child,\n                        level: level + 1,\n                        expandedNodes: expandedNodes,\n                        onToggleExpanded: onToggleExpanded,\n                        onEdit: onEdit,\n                        onDelete: onDelete,\n                        onAddChild: onAddChild,\n                        searchTerm: searchTerm\n                    }, child.id, false, {\n                        fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                        lineNumber: 698,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false)\n        ]\n    }, void 0, true);\n}\n_c1 = TableTreeNode;\n// 分类表单组件\nfunction CatalogForm(param) {\n    let { formData, setFormData, isEditing, isEditingExisting = false, onSave, onCancel, loading } = param;\n    const availableTags = [\n        \"color\",\n        \"size\",\n        \"model\",\n        \"material\",\n        \"style\"\n    ];\n    const updateFormData = (field, value)=>{\n        setFormData({\n            ...formData,\n            [field]: value\n        });\n    };\n    if (isEditing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"name\",\n                                    children: [\n                                        \"中文名称 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 40\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"name\",\n                                    value: formData.name || \"\",\n                                    onChange: (e)=>updateFormData(\"name\", e.target.value),\n                                    placeholder: \"请输入中文名称\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"englishName\",\n                                    children: [\n                                        \"英文名称 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 47\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 755,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"englishName\",\n                                    value: formData.englishName || \"\",\n                                    onChange: (e)=>updateFormData(\"englishName\", e.target.value),\n                                    placeholder: \"请输入英文名称\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 756,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 754,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 743,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"categoryCode\",\n                                    children: [\n                                        \"分类编码 \",\n                                        isEditingExisting ? \"(不可修改)\" : \"(可选)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 768,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"categoryCode\",\n                                    value: formData.categoryCode || \"\",\n                                    onChange: (e)=>{\n                                        if (!isEditingExisting) {\n                                            // 只有在创建新分类时才允许修改\n                                            const value = e.target.value.toUpperCase().replace(/[^A-Z0-9_-]/g, \"\");\n                                            updateFormData(\"categoryCode\", value);\n                                        }\n                                    },\n                                    placeholder: isEditingExisting ? \"分类编码创建后不可修改\" : \"如：CLOTHING\",\n                                    disabled: isEditingExisting,\n                                    className: isEditingExisting ? \"bg-muted cursor-not-allowed\" : \"\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 771,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: isEditingExisting ? \"分类编码在创建后不能修改，以确保数据一致性\" : \"用于自动生成SKU，支持字母数字下划线连字符，会自动转换为大写\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 785,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 767,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"status\",\n                                    children: \"状态\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 793,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                    value: formData.status || \"active\",\n                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                placeholder: \"选择状态\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 798,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: \"active\",\n                                                    children: \"启用\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 802,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: \"inactive\",\n                                                    children: \"禁用\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                                    lineNumber: 794,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 792,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 766,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"description\",\n                            children: \"描述\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 810,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                            id: \"description\",\n                            value: formData.description || \"\",\n                            onChange: (e)=>updateFormData(\"description\", e.target.value),\n                            placeholder: \"请输入目录描述\",\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 811,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 809,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"tags\",\n                            children: \"属性标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 821,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                            id: \"tags\",\n                            value: Array.isArray(formData.tags) ? formData.tags.join(\", \") : \"\",\n                            onChange: (e)=>updateFormData(\"tags\", e.target.value.split(\",\").map((t)=>t.trim()).filter((t)=>t)),\n                            placeholder: \"color, size\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 822,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: [\n                                \"可选标签: \",\n                                availableTags.join(\", \")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 828,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 820,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-end gap-2 pt-4 border-t\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            onClick: onCancel,\n                            disabled: loading,\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 837,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            onClick: onSave,\n                            disabled: loading,\n                            children: loading ? \"保存中...\" : \"确定\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                            lineNumber: 845,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n                    lineNumber: 836,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\在云端工作记录\\\\Github\\\\ikun_erp\\\\ikun\\\\frontend\\\\src\\\\components\\\\products\\\\product-catalog-page.tsx\",\n            lineNumber: 742,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n_c2 = CatalogForm;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ProductCatalogPage\");\n$RefreshReg$(_c1, \"TableTreeNode\");\n$RefreshReg$(_c2, \"CatalogForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/products/product-catalog-page.tsx\n"));

/***/ })

});